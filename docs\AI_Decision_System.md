# 五子棋AI决策系统说明

## 1. 整体架构
```mermaid
graph LR
A[玩家落子] --> B{AI决策}
B --> C[开局策略]
B --> D[启发式搜索]
D --> E[极小极大算法]
E --> F[评估函数]
F --> G[落子执行]
```

## 2. 核心算法

### 2.1 开局策略 (前3步)
```javascript
// 文件: script.js
async function findBestMoveAsync() {
  // 第一步: 中心落子
  if (pieceCount === 0) return center;

  // 第二步: 相邻位置落子
  if (pieceCount === 1) return adjacentMove;

  // 第三步: 镜像对称落子
  if (pieceCount === 2) return mirrorMove;
}
```

### 2.2 启发式搜索
```javascript
// 搜索范围: 已有棋子周围2格
const searchRange = 2;

for (let row=0; row<boardSize; row++) {
  for (let col=0; col<boardSize; col++) {
    if (board[row][col]) {
      // 收集周围空位
      collectCandidateMoves(row, col, searchRange);
    }
  }
}
```

### 2.3 极小极大算法
```javascript
function minimax(depth, isMaximizing, alpha, beta) {
  // 终止条件: 达到深度/胜负已分
  if (depth === 0 || gameOver) return evaluateBoard();
  
  // MAX层(AI): 最大化得分
  if (isMaximizing) {
    let best = -Infinity;
    for (move in candidateMoves) {
      best = Math.max(best, minimax(depth-1, false, alpha, beta));
      alpha = Math.max(alpha, best);
      if (beta <= alpha) break; // Alpha-Beta剪枝
    }
    return best;
  }
  // MIN层(玩家): 最小化得分
  else {
    // 对称逻辑
  }
}
```

## 3. 评估函数体系

### 3.1 棋型评分表
| 棋型 | 得分 | 说明 |
|------|------|------|
| 五连 | 100000 | 必胜棋型 |
| 活四 | 10000 | 下一步必胜 |
| 活三 | 1000 | 潜在威胁 |
| 活二 | 100 | 发展潜力 |
| 对手活四 | -10000 | 紧急防守 |

### 3.2 评估逻辑
```javascript
function scoreWindow(window, player) {
  // 统计玩家/对手/空位数量
  const playerCount = countPlayerStones(window, player);
  const opponentCount = countOpponentStones(window, player);
  const emptyCount = countEmptyPositions(window);
  
  // 根据棋型计分
  if (playerCount === 5) score += 100000;
  else if (playerCount === 4 && emptyCount === 1) score += 10000;
  // ...其他棋型判断
}
```

## 4. 性能优化策略

1. **缓存机制**：`evaluationCache` 避免重复计算
2. **搜索范围限制**：仅评估已有棋子周围2格
3. **异步让步**：`await new Promise()` 防止界面卡顿
4. **开局特判**：前3步跳过复杂计算

## 5. 决策流程图
```mermaid
sequenceDiagram
   玩家->>+AI: 落子完成
   AI->>决策系统: 获取候选位置
   决策系统->>评估系统: 生成评分
   评估系统->>决策系统: 返回评分
   决策系统->>极小极大: 深度搜索(depth=2)
   极小极大-->>AI: 返回最佳落子
   AI->>-棋盘: 执行落子
```

> 完整实现详见 `script.js` 中的 `findBestMoveAsync()` 和 `minimax()` 函数
