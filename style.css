body {
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
}

#board {
    display: grid;
    grid-template-columns: repeat(15, 40px);
    grid-template-rows: repeat(15, 40px);
    border: 2px solid #333;
    background-color: #f0d9b5;
}

.cell {
    width: 40px;
    height: 40px;
    border: 1px solid #999;
    box-sizing: border-box;
    position: relative;
}

.cell:hover {
    background-color: #c4a788;
}

.black::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background-color: black;
    border-radius: 50%;
}

.white::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background-color: white;
    border-radius: 50%;
    border: 1px solid #ccc;
}

#timers {
    display: flex;
    justify-content: space-between;
    width: 600px;
    margin-bottom: 10px;
}

#ai-thinking {
    margin-top: 10px;
    font-size: 18px;
    color: #333;
}

#reset-button {
    margin-top: 20px;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
}

.highlight {
    box-shadow: 0 0 15px 8px yellow;
}
