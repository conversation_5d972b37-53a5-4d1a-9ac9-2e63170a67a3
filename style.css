body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  margin: 0;
  background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
  color: white;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  max-width: 90vw;
}

h1 {
  margin-bottom: 1.5rem;
  font-size: 2.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  text-align: center;
}

#board {
  display: grid;
  grid-template-columns: repeat(15, 1fr);
  grid-template-rows: repeat(15, 1fr);
  gap: 1px;
  background-color: #cd853f;
  border: 3px solid #8b4513;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  margin: 1rem 0;
  width: 70vmin;
  height: 70vmin;
  max-width: 600px;
  max-height: 600px;
}

.cell {
  background-color: #f5deb3;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: background-color 0.2s;
  cursor: pointer;
  border: 1px solid #8b4513;
  box-sizing: border-box;
}

.cell:hover {
  background-color: #e6c9a1;
}

.cell.black::after {
  content: '';
  position: absolute;
  width: 85%;
  height: 85%;
  background-color: black;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.7);
}

.cell.white::after {
  content: '';
  position: absolute;
  width: 85%;
  height: 85%;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.cell.highlight::after {
  animation: pulse 0.5s ease-in-out 3;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.stats {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin: 1rem 0;
  font-size: 1.2rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem;
  border-radius: 8px;
}

#ai-thinking {
  font-size: 1.2rem;
  height: 1.5rem;
  margin: 0.5rem 0;
  font-weight: bold;
  color: #ffcc00;
  display: none;
}

button {
  background: linear-gradient(to right, #4a00e0, #8e2de2);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  font-size: 1.1rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-top: 1rem;
}

button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

button:active {
  transform: translateY(1px);
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
    transform: scale(0.9);
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  #board {
    width: 90vmin;
    height: 90vmin;
  }
}
