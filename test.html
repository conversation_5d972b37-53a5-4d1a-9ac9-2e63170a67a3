<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        #board {
            display: grid;
            grid-template-columns: repeat(15, 1fr);
            grid-template-rows: repeat(15, 1fr);
            gap: 1px;
            background-color: #cd853f;
            border: 2px solid #8b4513;
            width: 600px;
            height: 600px;
            margin: 20px 0;
        }
        
        .cell {
            background-color: #f5deb3;
            border: 1px solid #8b4513;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }
        
        .cell:hover {
            background-color: #e6c9a1;
        }
        
        .cell.black::after {
            content: '';
            position: absolute;
            width: 80%;
            height: 80%;
            background-color: black;
            border-radius: 50%;
        }
        
        .cell.white::after {
            content: '';
            position: absolute;
            width: 80%;
            height: 80%;
            background-color: white;
            border-radius: 50%;
            border: 1px solid #ccc;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>五子棋测试</h1>
    <div id="board"></div>
    <button onclick="resetGame()">重新开始</button>
    <div id="status">点击棋盘开始游戏</div>
    
    <script>
        const boardSize = 15;
        let board = [];
        let currentPlayer = 'black';
        let gameOver = false;
        
        function initBoard() {
            console.log('初始化棋盘...');
            board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
            const boardElement = document.getElementById('board');
            boardElement.innerHTML = '';
            
            for (let row = 0; row < boardSize; row++) {
                for (let col = 0; col < boardSize; col++) {
                    const cell = document.createElement('div');
                    cell.classList.add('cell');
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    cell.addEventListener('click', handleCellClick);
                    boardElement.appendChild(cell);
                }
            }
            
            currentPlayer = 'black';
            gameOver = false;
            document.getElementById('status').textContent = '黑棋先手，点击棋盘落子';
            console.log('棋盘初始化完成');
        }
        
        function handleCellClick(event) {
            console.log('单元格被点击');
            if (gameOver) return;
            
            const cell = event.target;
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);
            
            console.log(`点击位置: (${row}, ${col})`);
            
            if (board[row][col]) {
                console.log('位置已被占用');
                return;
            }
            
            // 放置棋子
            board[row][col] = currentPlayer;
            cell.classList.add(currentPlayer);
            
            console.log(`${currentPlayer} 棋子放置在 (${row}, ${col})`);
            
            // 检查获胜
            if (checkWin(row, col, currentPlayer)) {
                gameOver = true;
                document.getElementById('status').textContent = `${currentPlayer === 'black' ? '黑棋' : '白棋'}获胜！`;
                return;
            }
            
            // 切换玩家
            currentPlayer = currentPlayer === 'black' ? 'white' : 'black';
            document.getElementById('status').textContent = `轮到${currentPlayer === 'black' ? '黑棋' : '白棋'}`;
        }
        
        function checkWin(row, col, player) {
            const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];
            
            for (const [dx, dy] of directions) {
                let count = 1;
                
                // 向前计数
                for (let i = 1; i < 5; i++) {
                    const newRow = row + i * dx;
                    const newCol = col + i * dy;
                    if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && 
                        board[newRow][newCol] === player) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                // 向后计数
                for (let i = 1; i < 5; i++) {
                    const newRow = row - i * dx;
                    const newCol = col - i * dy;
                    if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && 
                        board[newRow][newCol] === player) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                if (count >= 5) {
                    return true;
                }
            }
            
            return false;
        }
        
        function resetGame() {
            console.log('重置游戏');
            initBoard();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化');
            initBoard();
        });
    </script>
</body>
</html>
