const boardElement = document.getElementById('board');
const resetButton = document.getElementById('reset-button');
const playerTimerElement = document.getElementById('player-timer');
const aiTimerElement = document.getElementById('ai-timer');
const aiThinkingElement = document.getElementById('ai-thinking');
const boardSize = 15;
let board = [];
let currentPlayer = 'black';
let gameOver = false;
let playerTime = 0;
let aiTime = 0;
let playerTimerInterval;
let aiTimerInterval;



// 一次性事件委托绑定（移到全局）
boardElement.addEventListener('click', (event) => {
    if (event.target.classList.contains('cell')) {
        handleCellClick(event);
    }
});

function initBoard() {
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    boardElement.innerHTML = '';
    // 清空评估缓存
    evaluationCache = {};
    
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            const cell = document.createElement('div');
            cell.classList.add('cell');
            cell.dataset.row = row;
            cell.dataset.col = col;
            boardElement.appendChild(cell);
        }
    }
    currentPlayer = 'black';
    gameOver = false;
    resetTimers();
    startPlayerTimer();
}

function handleCellClick(event) {
    if (gameOver || currentPlayer !== 'black') return;
    const row = parseInt(event.target.dataset.row);
    const col = parseInt(event.target.dataset.col);

    if (board[row][col]) return;

    stopPlayerTimer();
    board[row][col] = 'black';
    document.querySelector(`[data-row='${row}'][data-col='${col}']`).classList.add('black');

    if (checkWin(row, col, 'black')) {
        gameOver = true;
        setTimeout(() => alert('恭喜你，你赢了!'), 100);
        return;
    }

    currentPlayer = 'white';
    aiMove();
}

async function aiMove() {
    if (gameOver) return;
    aiThinkingElement.style.display = 'block';
    startAiTimer();

    const bestMove = await findBestMoveAsync();

    stopAiTimer();
    aiThinkingElement.style.display = 'none';

    if (bestMove) {
        const { row, col } = bestMove;
        board[row][col] = 'white';
        const cell = document.querySelector(`[data-row='${row}'][data-col='${col}']`);
        cell.classList.add('white');

        // AI赢了直接结束
        if (checkWin(row, col, 'white')) {
            gameOver = true; // 提前设置游戏结束
            // AI棋子闪烁三次，结束后弹窗
            let blinkCount = 0;
            let blinkTimer = setInterval(() => {
                cell.classList.toggle('highlight');
                blinkCount++;
                if (blinkCount >= 6) {
                    clearInterval(blinkTimer);
                    cell.classList.remove('highlight');
                    setTimeout(() => alert('AI 赢了!'), 100);
                }
            }, 200);
            return;
        }

        // AI棋子闪烁三次，结束后切换到玩家回合
        let blinkCount = 0;
        let blinkTimer = setInterval(() => {
            cell.classList.toggle('highlight');
            blinkCount++;
            if (blinkCount >= 6) {
                clearInterval(blinkTimer);
                cell.classList.remove('highlight');
                // 闪烁结束后才允许玩家落子
                currentPlayer = 'black';
                startPlayerTimer();
            }
        }, 200);
    } else {
        // 检查是否棋盘已满
        const isBoardFull = board.flat().every(cell => cell !== null);
        if (isBoardFull) {
            gameOver = true;
            setTimeout(() => alert('平局!'), 100);
        } else {
            // 如果AI找不到移动但棋盘未满，继续玩家回合
            currentPlayer = 'black';
            startPlayerTimer();
        }
        return;
    }
}

function resetTimers() {
    clearInterval(playerTimerInterval);
    clearInterval(aiTimerInterval);
    playerTimerInterval = null;
    aiTimerInterval = null;
    playerTime = 0;
    aiTime = 0;
    playerTimerElement.textContent = '玩家用时: 0s';
    aiTimerElement.textContent = 'AI用时: 0s';
}

function startPlayerTimer() {
    clearInterval(playerTimerInterval);
    playerTimerInterval = setInterval(() => {
        playerTime++;
        playerTimerElement.textContent = `玩家用时: ${playerTime}s`;
    }, 1000);
}

function stopPlayerTimer() {
    clearInterval(playerTimerInterval);
}

function startAiTimer() {
    clearInterval(aiTimerInterval);
    aiTimerInterval = setInterval(() => {
        aiTime++;
        aiTimerElement.textContent = `AI用时: ${aiTime}s`;
    }, 1000);
}

function stopAiTimer() {
    clearInterval(aiTimerInterval);
}

async function findBestMoveAsync() {
    let pieceCount = 0;
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c]) {
                pieceCount++;
            }
        }
    }

    // 开局前3步使用固定模式加速决策
    if (pieceCount <= 3) {
        const center = Math.floor(boardSize / 2);
        // 第一步：中心点
        if (pieceCount === 0) {
            return { row: center, col: center };
        }
        // 第二步：玩家落子后，AI选择相邻位置
        if (pieceCount === 1) {
            const adjacentMoves = [
                {row: center - 1, col: center - 1},
                {row: center - 1, col: center},
                {row: center - 1, col: center + 1},
                {row: center, col: center - 1},
                {row: center, col: center + 1},
                {row: center + 1, col: center - 1},
                {row: center + 1, col: center},
                {row: center + 1, col: center + 1},
            ];
            for(const move of adjacentMoves) {
                if(board[move.row][move.col] === null) {
                    return move;
                }
            }
        }
        // 第三步：根据玩家位置选择对称点
        if (pieceCount === 2) {
            const playerMove = findPlayerFirstMove();
            if (playerMove) {
                const mirrorRow = center + (center - playerMove.row);
                const mirrorCol = center + (center - playerMove.col);
                if (board[mirrorRow][mirrorCol] === null) {
                    return { row: mirrorRow, col: mirrorCol };
                }
            }
        }
    }

    let bestScore = -Infinity;
    let move = null;
    const depth = 2; // 恢复深度2层平衡棋力

    // 启发式搜索：只搜索有棋子周围2格内的空位
    const candidateMoves = [];
    const searchRange = 2; // 缩小搜索范围
    
    // 收集所有已有棋子周围2格内的空位
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
                    for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
                        if (!board[r][c] && !candidateMoves.some(m => m.row === r && m.col === c)) {
                            candidateMoves.push({ row: r, col: c });
                        }
                    }
                }
            }
        }
    }
    
    // 如果没有候选位置，使用中心区域
    if (candidateMoves.length === 0) {
        const center = Math.floor(boardSize / 2);
        candidateMoves.push({ row: center, col: center });
    }

    // 评估候选位置（每评估一个位置就yield）
    for (const { row, col } of candidateMoves) {
        if (!board[row][col]) {
            board[row][col] = 'white';
            let score = minimax(depth - 1, false, -Infinity, Infinity);
            board[row][col] = null;
            if (score > bestScore) {
                bestScore = score;
                move = { row, col };
            }
        }
        // 每个位置都yield确保界面响应
        await new Promise(resolve => setTimeout(resolve, 0));
    }
    return move;
}

// 辅助函数：找到玩家第一步落子位置
function findPlayerFirstMove() {
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c] === 'black') {
                return { row: r, col: c };
            }
        }
    }
    return null;
}

function minimax(depth, isMaximizing, alpha, beta) {
    let score = evaluateBoard();
    if (score >= 100000 || score <= -100000 || depth === 0) {
        return score;
    }

    if (isMaximizing) {
        let best = -Infinity;
        for (let row = 0; row < boardSize; row++) {
            for (let col = 0; col < boardSize; col++) {
                if (!board[row][col]) {
                    board[row][col] = 'white';
                    best = Math.max(best, minimax(depth - 1, !isMaximizing, alpha, beta));
                    board[row][col] = null;
                    alpha = Math.max(alpha, best);
                    if (beta <= alpha) {
                        break;
                    }
                }
            }
            if (beta <= alpha) {
                break;
            }
        }
        return best;
    } else {
        let best = Infinity;
        for (let row = 0; row < boardSize; row++) {
            for (let col = 0; col < boardSize; col++) {
                if (!board[row][col]) {
                    board[row][col] = 'black';
                    best = Math.min(best, minimax(depth - 1, !isMaximizing, alpha, beta));
                    board[row][col] = null;
                    beta = Math.min(beta, best);
                    if (beta <= alpha) {
                        break;
                    }
                }
            }
            if (beta <= alpha) {
                break;
            }
        }
        return best;
    }
}

// 缓存评估结果避免重复计算
let evaluationCache = {};
function evaluateBoard() {
    const boardKey = JSON.stringify(board);
    if (evaluationCache[boardKey]) {
        return evaluationCache[boardKey];
    }
    
    let score = 0;
    score += evaluateLines(board); // Rows
    let transposedBoard = transpose(board);
    score += evaluateLines(transposedBoard); // Columns
    let diagonals = getDiagonals(board);
    score += evaluateLines(diagonals); // Diagonals
    
    evaluationCache[boardKey] = score;
    return score;
}

function evaluateLines(lines) {
    let score = 0;
    for (const line of lines) {
        score += evaluateLine(line);
    }
    return score;
}

function evaluateLine(line) {
    let score = 0;
    // 恢复滑动窗口评估（5个连续位置）
    for (let i = 0; i <= line.length - 5; i++) {
        let window = line.slice(i, i + 5);
        score += scoreWindow(window, 'white') - scoreWindow(window, 'black') * 1.2; // 调整权重比例
    }
    return score;
}

function scoreWindow(window, player) {
    let score = 0;
    let playerCount = window.filter(cell => cell === player).length;
    let opponent = player === 'white' ? 'black' : 'white';
    let opponentCount = window.filter(cell => cell === opponent).length;
    let emptyCount = window.filter(cell => cell === null).length;

    // 简化评估函数，专注于核心棋型
    if (playerCount === 5) {
        score += 100000; // 五连
    } else if (playerCount === 4 && emptyCount === 1) {
        score += 10000; // 活四
    } else if (playerCount === 3 && emptyCount === 2) {
        score += 1000; // 活三
    } else if (playerCount === 2 && emptyCount === 3) {
        score += 100; // 活二
    }
    
    // 防守评估
    if (opponentCount === 4 && emptyCount === 1) {
        score -= 10000; // 对手活四
    } else if (opponentCount === 3 && emptyCount === 2) {
        score -= 1000; // 对手活三
    }

    return score;
}

function transpose(matrix) {
    return matrix[0].map((col, i) => matrix.map(row => row[i]));
}

function getDiagonals(matrix) {
    const diagonals = [];
    const n = matrix.length;
    const m = matrix[0].length;

    for (let k = 0; k < n + m - 1; k++) {
        const diag = [];
        for (let j = 0; j <= k; j++) {
            const i = k - j;
            if (i < n && j < m) {
                diag.push(matrix[i][j]);
            }
        }
        if (diag.length >= 5) diagonals.push(diag);
    }

    for (let k = 0; k < n + m - 1; k++) {
        const diag = [];
        for (let j = 0; j <= k; j++) {
            const i = k - j;
            if (i < n && j < m) {
                diag.push(matrix[i][m - 1 - j]);
            }
        }
        if (diag.length >= 5) diagonals.push(diag);
    }
    return diagonals;
}

function checkWin(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        if (count >= 5) {
            return true;
        }
    }
    return false;
}

resetButton.addEventListener('click', initBoard);

initBoard();
