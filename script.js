const boardElement = document.getElementById('board');
const resetButton = document.getElementById('reset-button');
const playerTimerElement = document.getElementById('player-timer');
const aiTimerElement = document.getElementById('ai-timer');
const aiThinkingElement = document.getElementById('ai-thinking');
const boardSize = 15;
let board = [];
let currentPlayer = 'black';
let gameOver = false;
let playerTime = 0;
let aiTime = 0;
let playerTimerInterval;
let aiTimerInterval;



// 一次性事件委托绑定（移到全局）
boardElement.addEventListener('click', (event) => {
    if (event.target.classList.contains('cell')) {
        handleCellClick(event);
    }
});

function initBoard() {
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    boardElement.innerHTML = '';
    // 清空所有缓存
    clearCaches();

    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            const cell = document.createElement('div');
            cell.classList.add('cell');
            cell.dataset.row = row;
            cell.dataset.col = col;
            boardElement.appendChild(cell);
        }
    }
    currentPlayer = 'black';
    gameOver = false;
    resetTimers();
    startPlayerTimer();
}

function handleCellClick(event) {
    if (gameOver || currentPlayer !== 'black') return;
    const row = parseInt(event.target.dataset.row);
    const col = parseInt(event.target.dataset.col);

    if (board[row][col]) return;

    stopPlayerTimer();
    board[row][col] = 'black';
    document.querySelector(`[data-row='${row}'][data-col='${col}']`).classList.add('black');

    if (checkWin(row, col, 'black')) {
        gameOver = true;
        setTimeout(() => alert('恭喜你，你赢了!'), 100);
        return;
    }

    currentPlayer = 'white';
    aiMove();
}

async function aiMove() {
    if (gameOver) return;
    aiThinkingElement.style.display = 'block';
    startAiTimer();

    const bestMove = await findBestMoveAsync();

    stopAiTimer();
    aiThinkingElement.style.display = 'none';

    if (bestMove) {
        const { row, col } = bestMove;
        board[row][col] = 'white';
        const cell = document.querySelector(`[data-row='${row}'][data-col='${col}']`);
        cell.classList.add('white');

        // AI赢了直接结束
        if (checkWin(row, col, 'white')) {
            gameOver = true; // 提前设置游戏结束
            // AI棋子闪烁三次，结束后弹窗
            let blinkCount = 0;
            let blinkTimer = setInterval(() => {
                cell.classList.toggle('highlight');
                blinkCount++;
                if (blinkCount >= 6) {
                    clearInterval(blinkTimer);
                    cell.classList.remove('highlight');
                    setTimeout(() => alert('AI 赢了!'), 100);
                }
            }, 200);
            return;
        }

        // AI棋子闪烁三次，结束后切换到玩家回合
        let blinkCount = 0;
        let blinkTimer = setInterval(() => {
            cell.classList.toggle('highlight');
            blinkCount++;
            if (blinkCount >= 6) {
                clearInterval(blinkTimer);
                cell.classList.remove('highlight');
                // 闪烁结束后才允许玩家落子
                currentPlayer = 'black';
                startPlayerTimer();
            }
        }, 200);
    } else {
        // 检查是否棋盘已满
        const isBoardFull = board.flat().every(cell => cell !== null);
        if (isBoardFull) {
            gameOver = true;
            setTimeout(() => alert('平局!'), 100);
        } else {
            // 如果AI找不到移动但棋盘未满，继续玩家回合
            currentPlayer = 'black';
            startPlayerTimer();
        }
        return;
    }
}

function resetTimers() {
    clearInterval(playerTimerInterval);
    clearInterval(aiTimerInterval);
    playerTimerInterval = null;
    aiTimerInterval = null;
    playerTime = 0;
    aiTime = 0;
    playerTimerElement.textContent = '玩家用时: 0s';
    aiTimerElement.textContent = 'AI用时: 0s';
}

function startPlayerTimer() {
    clearInterval(playerTimerInterval);
    playerTimerInterval = setInterval(() => {
        playerTime++;
        playerTimerElement.textContent = `玩家用时: ${playerTime}s`;
    }, 1000);
}

function stopPlayerTimer() {
    clearInterval(playerTimerInterval);
}

function startAiTimer() {
    clearInterval(aiTimerInterval);
    aiTimerInterval = setInterval(() => {
        aiTime++;
        aiTimerElement.textContent = `AI用时: ${aiTime}s`;
    }, 1000);
}

function stopAiTimer() {
    clearInterval(aiTimerInterval);
}

async function findBestMoveAsync() {
    let pieceCount = 0;
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c]) {
                pieceCount++;
            }
        }
    }

    // 改进的开局策略
    if (pieceCount <= 5) {
        const openingMove = getOpeningMove(pieceCount);
        if (openingMove) {
            return openingMove;
        }
    }

    // 首先检查是否有立即获胜的机会
    const winningMove = findWinningMove('white');
    if (winningMove) {
        return winningMove;
    }

    // 检查是否需要紧急防守
    const urgentDefense = findUrgentDefense();
    if (urgentDefense) {
        return urgentDefense;
    }

    // 检查是否需要防守（阻止对手获胜）
    const blockingMove = findWinningMove('black');
    if (blockingMove) {
        return blockingMove;
    }

    // 检查是否有创造双威胁的机会
    const doubleThreatMove = findDoubleThreatMove();
    if (doubleThreatMove) {
        return doubleThreatMove;
    }

    // 使用迭代加深搜索
    return await iterativeDeepeningSearch(pieceCount);
}

// 迭代加深搜索实现
async function iterativeDeepeningSearch(pieceCount) {
    let bestMove = null;
    let bestScore = -Infinity;

    // 根据棋盘复杂度确定最大搜索深度
    const maxDepth = pieceCount < 8 ? 6 : pieceCount < 16 ? 5 : 4;
    const timeLimit = 3000; // 3秒时间限制
    const startTime = Date.now();

    // 获取排序后的候选位置
    const candidateMoves = getSortedCandidateMoves();
    const maxCandidates = Math.min(candidateMoves.length, 20);

    // 置换表用于存储搜索结果
    const transpositionTable = new Map();

    // 从深度1开始迭代加深
    for (let depth = 1; depth <= maxDepth; depth++) {
        let currentBestMove = null;
        let currentBestScore = -Infinity;

        // 检查时间限制
        if (Date.now() - startTime > timeLimit) {
            break;
        }

        // 搜索所有候选位置
        for (let i = 0; i < maxCandidates; i++) {
            const { row, col } = candidateMoves[i];

            if (!board[row][col]) {
                board[row][col] = 'white';

                // 使用置换表优化的minimax搜索
                const score = minimaxWithTT(depth - 1, false, -Infinity, Infinity, transpositionTable);

                board[row][col] = null;

                if (score > currentBestScore) {
                    currentBestScore = score;
                    currentBestMove = { row, col };
                }

                // 检查时间限制
                if (Date.now() - startTime > timeLimit) {
                    break;
                }
            }

            // 每个位置都yield确保界面响应
            await new Promise(resolve => setTimeout(resolve, 0));
        }

        // 如果找到了更好的移动，更新最佳移动
        if (currentBestMove && currentBestScore > bestScore) {
            bestMove = currentBestMove;
            bestScore = currentBestScore;
        }

        // 如果找到必胜移动，提前结束
        if (bestScore >= 50000) {
            break;
        }
    }

    return bestMove;
}

// 改进的开局策略
function getOpeningMove(pieceCount) {
    const center = Math.floor(boardSize / 2);

    // 第一步：中心点
    if (pieceCount === 0) {
        return { row: center, col: center };
    }

    // 获取玩家的开局模式
    const playerMoves = getPlayerMoves();

    // 根据玩家开局选择对应策略
    if (pieceCount === 1) {
        return getSecondMove(playerMoves[0], center);
    }

    if (pieceCount === 2) {
        return getThirdMove(playerMoves, center);
    }

    if (pieceCount === 3) {
        return getFourthMove(playerMoves, center);
    }

    if (pieceCount === 4) {
        return getFifthMove();
    }

    // 后续开局：使用评估函数
    if (pieceCount <= 8) {
        const candidates = getSortedCandidateMoves();
        if (candidates.length > 0) {
            return candidates[0];
        }
    }

    return null;
}

// 获取玩家的所有落子位置
function getPlayerMoves() {
    const moves = [];
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c] === 'black') {
                moves.push({ row: r, col: c });
            }
        }
    }
    return moves;
}

// AI第二步策略
function getSecondMove(playerFirstMove, center) {
    const dx = playerFirstMove.row - center;
    const dy = playerFirstMove.col - center;

    // 根据玩家第一步的位置选择不同的开局
    if (dx === 0 && dy === 0) {
        // 玩家下在中心，AI选择天元开局
        return { row: center - 1, col: center - 1 };
    } else if (Math.abs(dx) <= 1 && Math.abs(dy) <= 1) {
        // 玩家在中心附近，使用星月开局
        return getStarMoonOpening(playerFirstMove, center);
    } else {
        // 玩家远离中心，使用花月开局
        return getFlowerMoonOpening(playerFirstMove, center);
    }
}

// 星月开局
function getStarMoonOpening(playerMove, center) {
    const candidates = [
        { row: center - 1, col: center - 1 },
        { row: center - 1, col: center + 1 },
        { row: center + 1, col: center - 1 },
        { row: center + 1, col: center + 1 },
    ];

    // 选择与玩家棋子形成对角的位置
    for (const candidate of candidates) {
        if (board[candidate.row][candidate.col] === null) {
            const distance = Math.abs(candidate.row - playerMove.row) + Math.abs(candidate.col - playerMove.col);
            if (distance === 2) {
                return candidate;
            }
        }
    }

    // 如果没有合适的对角位置，选择第一个可用位置
    return candidates.find(pos => board[pos.row][pos.col] === null);
}

// 花月开局
function getFlowerMoonOpening(playerMove, center) {
    // 在中心附近选择一个平衡的位置，避开玩家棋子方向
    const candidates = [
        { row: center, col: center - 1 },
        { row: center, col: center + 1 },
        { row: center - 1, col: center },
        { row: center + 1, col: center },
    ];

    // 优先选择远离玩家棋子的位置
    candidates.sort((a, b) => {
        const distA = Math.abs(a.row - playerMove.row) + Math.abs(a.col - playerMove.col);
        const distB = Math.abs(b.row - playerMove.row) + Math.abs(b.col - playerMove.col);
        return distB - distA;
    });

    return candidates.find(pos => board[pos.row][pos.col] === null) || candidates[0];
}

// AI第三步策略
function getThirdMove(playerMoves, center) {
    if (playerMoves.length < 2) return null;

    // 分析玩家的布局意图
    const pattern = analyzePlayerPattern(playerMoves, center);

    switch (pattern) {
        case 'aggressive':
            return getDefensiveThirdMove(playerMoves, center);
        case 'balanced':
            return getBalancedThirdMove(playerMoves, center);
        case 'defensive':
            return getAggressiveThirdMove(playerMoves, center);
        default:
            return getDefaultThirdMove(center);
    }
}

// AI第四步策略
function getFourthMove(playerMoves, center) {
    // 在第四步开始更多依赖评估函数
    const candidates = getSortedCandidateMoves();

    // 但仍然考虑一些开局原则和玩家位置
    const filteredCandidates = candidates.filter(move => {
        const distance = Math.abs(move.row - center) + Math.abs(move.col - center);

        // 避免过于靠近玩家密集区域
        let tooClose = false;
        for (const playerMove of playerMoves) {
            const playerDistance = Math.abs(move.row - playerMove.row) + Math.abs(move.col - playerMove.col);
            if (playerDistance === 1) {
                tooClose = true;
                break;
            }
        }

        return distance <= 4 && !tooClose; // 保持在中心区域且不太靠近玩家
    });

    return filteredCandidates.length > 0 ? filteredCandidates[0] : candidates[0];
}

// AI第五步策略
function getFifthMove() {
    // 第五步完全依赖评估函数，但优先考虑攻击性位置
    const candidates = getSortedCandidateMoves();

    // 寻找能形成威胁的位置
    for (const candidate of candidates.slice(0, 5)) {
        board[candidate.row][candidate.col] = 'white';
        const threats = analyzePositionThreats(candidate.row, candidate.col, 'white');
        board[candidate.row][candidate.col] = null;

        if (threats.activeThrees > 0 || threats.rushFours > 0) {
            return candidate;
        }
    }

    return candidates[0];
}

// 分析玩家开局模式
function analyzePlayerPattern(playerMoves, center) {
    if (playerMoves.length < 2) return 'balanced';

    let aggressiveScore = 0;
    let defensiveScore = 0;

    for (const move of playerMoves) {
        const distance = Math.abs(move.row - center) + Math.abs(move.col - center);

        if (distance <= 2) {
            aggressiveScore += 2; // 靠近中心，较为激进
        } else {
            defensiveScore += 1; // 远离中心，较为保守
        }
    }

    if (aggressiveScore > defensiveScore * 1.5) {
        return 'aggressive';
    } else if (defensiveScore > aggressiveScore * 1.5) {
        return 'defensive';
    } else {
        return 'balanced';
    }
}

// 防守型第三步
function getDefensiveThirdMove(playerMoves, center) {
    // 在玩家棋子之间插入，破坏其连接
    const candidates = [];

    for (let i = 0; i < playerMoves.length; i++) {
        for (let j = i + 1; j < playerMoves.length; j++) {
            const midRow = Math.floor((playerMoves[i].row + playerMoves[j].row) / 2);
            const midCol = Math.floor((playerMoves[i].col + playerMoves[j].col) / 2);

            if (isValidPosition(midRow, midCol) && board[midRow][midCol] === null) {
                candidates.push({ row: midRow, col: midCol });
            }
        }
    }

    return candidates.length > 0 ? candidates[0] : getDefaultThirdMove(center);
}

// 平衡型第三步
function getBalancedThirdMove(_, center) {
    // 选择一个既不太激进也不太保守的位置
    const candidates = [
        { row: center - 2, col: center },
        { row: center + 2, col: center },
        { row: center, col: center - 2 },
        { row: center, col: center + 2 },
    ];

    return candidates.find(pos => board[pos.row][pos.col] === null) || getDefaultThirdMove(center);
}

// 攻击型第三步
function getAggressiveThirdMove(_, center) {
    // 选择一个能形成攻击的位置
    const candidates = getSortedCandidateMoves();

    for (const candidate of candidates.slice(0, 3)) {
        board[candidate.row][candidate.col] = 'white';
        const score = evaluateBoard();
        board[candidate.row][candidate.col] = null;

        if (score > 1000) { // 如果能形成较好的棋型
            return candidate;
        }
    }

    return getDefaultThirdMove(center);
}

// 默认第三步
function getDefaultThirdMove(center) {
    const candidates = [
        { row: center - 1, col: center - 2 },
        { row: center - 1, col: center + 2 },
        { row: center + 1, col: center - 2 },
        { row: center + 1, col: center + 2 },
    ];

    return candidates.find(pos => isValidPosition(pos.row, pos.col) && board[pos.row][pos.col] === null) ||
           { row: center - 2, col: center - 2 };
}

// 寻找获胜位置
function findWinningMove(player) {
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (!board[row][col]) {
                board[row][col] = player;
                if (checkWin(row, col, player)) {
                    board[row][col] = null;
                    return { row, col };
                }
                board[row][col] = null;
            }
        }
    }
    return null;
}

// 增强的威胁检测系统
function findDoubleThreatMove() {
    // 寻找能创造多重威胁的位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (!board[row][col]) {
                board[row][col] = 'white';
                const newThreats = analyzePositionThreats(row, col, 'white');
                board[row][col] = null;

                // 如果能创造双威胁或更多
                if (newThreats.activeFours >= 2 ||
                    (newThreats.activeFours >= 1 && newThreats.activeThrees >= 1) ||
                    newThreats.activeThrees >= 2) {
                    return { row, col };
                }
            }
        }
    }
    return null;
}

// 分析所有威胁
function analyzeAllThreats() {
    const threats = {
        white: { activeFours: [], activeThrees: [], rushFours: [] },
        black: { activeFours: [], activeThrees: [], rushFours: [] }
    };

    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (!board[row][col]) {
                // 分析白棋威胁
                board[row][col] = 'white';
                const whiteThreat = analyzePositionThreats(row, col, 'white');
                if (whiteThreat.activeFours > 0) threats.white.activeFours.push({row, col});
                if (whiteThreat.activeThrees > 0) threats.white.activeThrees.push({row, col});
                if (whiteThreat.rushFours > 0) threats.white.rushFours.push({row, col});
                board[row][col] = null;

                // 分析黑棋威胁
                board[row][col] = 'black';
                const blackThreat = analyzePositionThreats(row, col, 'black');
                if (blackThreat.activeFours > 0) threats.black.activeFours.push({row, col});
                if (blackThreat.activeThrees > 0) threats.black.activeThrees.push({row, col});
                if (blackThreat.rushFours > 0) threats.black.rushFours.push({row, col});
                board[row][col] = null;
            }
        }
    }

    return threats;
}

// 分析单个位置的威胁
function analyzePositionThreats(row, col, player) {
    const threats = { activeFours: 0, activeThrees: 0, rushFours: 0 };
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        const line = getLineFromPosition(row, col, dx, dy, 9);
        const centerIndex = Math.floor(line.length / 2);

        // 检查5格窗口
        for (let i = Math.max(0, centerIndex - 4); i <= Math.min(line.length - 5, centerIndex); i++) {
            const window = line.slice(i, i + 5);
            const playerCount = window.filter(cell => cell === player).length;
            const emptyCount = window.filter(cell => cell === null).length;
            const opponent = player === 'white' ? 'black' : 'white';
            const opponentCount = window.filter(cell => cell === opponent).length;

            if (opponentCount === 0) {
                if (playerCount === 4 && emptyCount === 1) {
                    if (isLiveFour(window, player)) {
                        threats.activeFours++;
                    } else {
                        threats.rushFours++;
                    }
                } else if (playerCount === 3 && emptyCount === 2) {
                    if (isLiveThree(window, player)) {
                        threats.activeThrees++;
                    }
                }
            }
        }
    }

    return threats;
}

// 获取从指定位置延伸的直线
function getLineFromPosition(row, col, dx, dy, length) {
    const line = [];
    const halfLength = Math.floor(length / 2);

    for (let i = -halfLength; i <= halfLength; i++) {
        const newRow = row + i * dx;
        const newCol = col + i * dy;

        if (isValidPosition(newRow, newCol)) {
            line.push(board[newRow][newCol]);
        } else {
            line.push('boundary'); // 边界标记
        }
    }

    return line;
}

// 寻找最紧急的防守位置
function findUrgentDefense() {
    const threats = analyzeAllThreats();

    // 优先级：对手活四 > 对手双三 > 对手活三
    if (threats.black.activeFours.length > 0) {
        return threats.black.activeFours[0];
    }

    if (threats.black.activeThrees.length >= 2) {
        // 寻找能同时防守多个活三的位置
        return findMultiDefensePosition(threats.black.activeThrees);
    }

    if (threats.black.activeThrees.length > 0) {
        return threats.black.activeThrees[0];
    }

    return null;
}

// 寻找能防守多个威胁的位置
function findMultiDefensePosition(threats) {
    const defenseMap = new Map();

    for (const threat of threats) {
        // 分析每个威胁周围的防守点
        const defensePoints = getDefensePoints(threat.row, threat.col);
        for (const point of defensePoints) {
            const key = `${point.row},${point.col}`;
            defenseMap.set(key, (defenseMap.get(key) || 0) + 1);
        }
    }

    // 找到能防守最多威胁的位置
    let bestDefense = null;
    let maxDefenseCount = 0;

    for (const [key, count] of defenseMap) {
        if (count > maxDefenseCount) {
            maxDefenseCount = count;
            const [row, col] = key.split(',').map(Number);
            bestDefense = { row, col };
        }
    }

    return bestDefense;
}

// 获取威胁的防守点
function getDefensePoints(row, col) {
    const defensePoints = [];
    const searchRange = 2;

    for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
        for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
            if (!board[r][c]) {
                defensePoints.push({ row: r, col: c });
            }
        }
    }

    return defensePoints;
}

// 获取排序后的候选位置
function getSortedCandidateMoves() {
    const candidateMoves = [];
    const searchRange = 2;

    // 收集候选位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
                    for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
                        if (!board[r][c] && !candidateMoves.some(m => m.row === r && m.col === c)) {
                            candidateMoves.push({ row: r, col: c });
                        }
                    }
                }
            }
        }
    }

    // 如果没有候选位置，使用中心区域
    if (candidateMoves.length === 0) {
        const center = Math.floor(boardSize / 2);
        candidateMoves.push({ row: center, col: center });
    }

    // 按位置价值排序
    candidateMoves.sort((a, b) => {
        const scoreA = evaluatePosition(a.row, a.col);
        const scoreB = evaluatePosition(b.row, b.col);
        return scoreB - scoreA;
    });

    return candidateMoves;
}

// 评估单个位置的价值
function evaluatePosition(row, col) {
    let score = 0;

    // 模拟放置白棋
    board[row][col] = 'white';
    const whiteScore = evaluateBoard();
    const whiteSpecial = checkSpecialPatterns(row, col, 'white');
    score += whiteScore + whiteSpecial;
    board[row][col] = null;

    // 模拟放置黑棋（防守价值）
    board[row][col] = 'black';
    const blackScore = evaluateBoard();
    const blackSpecial = checkSpecialPatterns(row, col, 'black');
    score -= (blackScore + blackSpecial) * 0.9;
    board[row][col] = null;

    // 位置权重分析
    score += calculatePositionWeight(row, col);

    // 连接性分析
    score += analyzeConnectivity(row, col);

    // 方向性分析
    score += analyzeDirectionality(row, col);

    return score;
}

// 计算位置权重
function calculatePositionWeight(row, col) {
    let weight = 0;
    const center = Math.floor(boardSize / 2);

    // 中心区域权重
    const distanceFromCenter = Math.abs(row - center) + Math.abs(col - center);
    weight += (boardSize - distanceFromCenter) * 15;

    // 边角惩罚
    if (row === 0 || row === boardSize - 1 || col === 0 || col === boardSize - 1) {
        weight -= 50;
    }

    // 次边惩罚
    if (row === 1 || row === boardSize - 2 || col === 1 || col === boardSize - 2) {
        weight -= 20;
    }

    return weight;
}

// 分析连接性
function analyzeConnectivity(row, col) {
    let connectivity = 0;
    const searchRange = 3;

    // 检查周围已有棋子的连接潜力
    for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
        for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
            if (board[r][c]) {
                const distance = Math.abs(r - row) + Math.abs(c - col);
                if (distance <= 2) {
                    // 距离越近，连接价值越高
                    connectivity += (3 - distance) * 20;

                    // 如果是同色棋子，额外加分
                    if (board[r][c] === 'white') {
                        connectivity += 30;
                    }
                }
            }
        }
    }

    return connectivity;
}

// 分析方向性
function analyzeDirectionality(row, col) {
    let directionality = 0;
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        let directionScore = 0;

        // 检查正方向
        let consecutiveEmpty = 0;
        let consecutiveWhite = 0;
        for (let i = 1; i <= 4; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;

            if (!isValidPosition(newRow, newCol)) break;

            if (board[newRow][newCol] === null) {
                consecutiveEmpty++;
            } else if (board[newRow][newCol] === 'white') {
                consecutiveWhite++;
                break;
            } else {
                break; // 遇到黑棋
            }
        }

        // 检查反方向
        let reverseEmpty = 0;
        let reverseWhite = 0;
        for (let i = 1; i <= 4; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;

            if (!isValidPosition(newRow, newCol)) break;

            if (board[newRow][newCol] === null) {
                reverseEmpty++;
            } else if (board[newRow][newCol] === 'white') {
                reverseWhite++;
                break;
            } else {
                break; // 遇到黑棋
            }
        }

        // 计算方向得分
        const totalEmpty = consecutiveEmpty + reverseEmpty;
        const totalWhite = consecutiveWhite + reverseWhite;

        if (totalEmpty >= 4) {
            directionScore += 100; // 有足够空间形成五连
        }

        if (totalWhite > 0) {
            directionScore += totalWhite * 50; // 有同色棋子支持
        }

        directionality += directionScore;
    }

    return directionality;
}

// 辅助函数：检查位置是否有效
function isValidPosition(row, col) {
    return row >= 0 && row < boardSize && col >= 0 && col < boardSize;
}

// 计算连续棋子数量
function countConsecutive(row, col, dx, dy, player) {
    let count = 1;

    // 向前计算
    for (let i = 1; i < 5; i++) {
        const newRow = row + i * dx;
        const newCol = col + i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else {
            break;
        }
    }

    // 向后计算
    for (let i = 1; i < 5; i++) {
        const newRow = row - i * dx;
        const newCol = col - i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else {
            break;
        }
    }

    return count;
}

// 辅助函数：找到玩家第一步落子位置
function findPlayerFirstMove() {
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c] === 'black') {
                return { row: r, col: c };
            }
        }
    }
    return null;
}

// 带置换表的minimax算法
function minimaxWithTT(depth, isMaximizing, alpha, beta, transpositionTable) {
    // 生成当前棋盘状态的哈希键
    const boardHash = getBoardHash();

    // 查找置换表
    if (transpositionTable.has(boardHash)) {
        const entry = transpositionTable.get(boardHash);
        if (entry.depth >= depth) {
            if (entry.type === 'exact') {
                return entry.value;
            } else if (entry.type === 'lowerbound' && entry.value >= beta) {
                return entry.value;
            } else if (entry.type === 'upperbound' && entry.value <= alpha) {
                return entry.value;
            }
        }
    }

    let score = evaluateBoard();

    // 终止条件：达到最大深度或游戏结束
    if (score >= 100000 || score <= -100000 || depth === 0) {
        // 存储到置换表
        transpositionTable.set(boardHash, {
            value: score,
            depth: depth,
            type: 'exact'
        });
        return score;
    }

    // 获取当前层的候选位置
    const candidateMoves = getMiniMaxCandidates();
    const originalAlpha = alpha;
    let bestValue;

    if (isMaximizing) {
        bestValue = -Infinity;

        // 按启发式价值排序候选位置
        candidateMoves.sort((a, b) => {
            board[a.row][a.col] = 'white';
            const scoreA = evaluateBoard();
            board[a.row][a.col] = null;

            board[b.row][b.col] = 'white';
            const scoreB = evaluateBoard();
            board[b.row][b.col] = null;

            return scoreB - scoreA;
        });

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'white';
            const value = minimaxWithTT(depth - 1, false, alpha, beta, transpositionTable);
            board[row][col] = null;

            bestValue = Math.max(bestValue, value);
            alpha = Math.max(alpha, bestValue);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
    } else {
        bestValue = Infinity;

        // 按启发式价值排序候选位置
        candidateMoves.sort((a, b) => {
            board[a.row][a.col] = 'black';
            const scoreA = evaluateBoard();
            board[a.row][a.col] = null;

            board[b.row][b.col] = 'black';
            const scoreB = evaluateBoard();
            board[b.row][b.col] = null;

            return scoreA - scoreB;
        });

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'black';
            const value = minimaxWithTT(depth - 1, true, alpha, beta, transpositionTable);
            board[row][col] = null;

            bestValue = Math.min(bestValue, value);
            beta = Math.min(beta, bestValue);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
    }

    // 存储到置换表
    let entryType;
    if (bestValue <= originalAlpha) {
        entryType = 'upperbound';
    } else if (bestValue >= beta) {
        entryType = 'lowerbound';
    } else {
        entryType = 'exact';
    }

    transpositionTable.set(boardHash, {
        value: bestValue,
        depth: depth,
        type: entryType
    });

    return bestValue;
}

// 生成棋盘状态的哈希值
function getBoardHash() {
    let hash = '';
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === 'white') {
                hash += '1';
            } else if (board[row][col] === 'black') {
                hash += '2';
            } else {
                hash += '0';
            }
        }
    }
    return hash;
}

// 保留原有的minimax函数作为备用
function minimax(depth, isMaximizing, alpha, beta) {
    let score = evaluateBoard();

    // 终止条件：达到最大深度或游戏结束
    if (score >= 100000 || score <= -100000 || depth === 0) {
        return score;
    }

    // 获取当前层的候选位置（只搜索有意义的位置）
    const candidateMoves = getMiniMaxCandidates();

    if (isMaximizing) {
        let best = -Infinity;

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'white';
            const value = minimax(depth - 1, false, alpha, beta);
            board[row][col] = null;

            best = Math.max(best, value);
            alpha = Math.max(alpha, best);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
        return best;
    } else {
        let best = Infinity;

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'black';
            const value = minimax(depth - 1, true, alpha, beta);
            board[row][col] = null;

            best = Math.min(best, value);
            beta = Math.min(beta, best);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
        return best;
    }
}

// 获取minimax搜索的候选位置
function getMiniMaxCandidates() {
    const candidates = [];
    const searchRange = 2;

    // 收集所有已有棋子周围的空位
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
                    for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
                        if (!board[r][c] && !candidates.some(m => m.row === r && m.col === c)) {
                            candidates.push({ row: r, col: c });
                        }
                    }
                }
            }
        }
    }

    // 限制候选位置数量以提高搜索效率
    return candidates.slice(0, 12);
}

// 改进的缓存系统
let evaluationCache = new Map();
let cacheHits = 0;
let cacheMisses = 0;
const MAX_CACHE_SIZE = 10000;

function evaluateBoard() {
    const boardHash = getBoardHash();

    // 检查缓存
    if (evaluationCache.has(boardHash)) {
        cacheHits++;
        return evaluationCache.get(boardHash);
    }

    cacheMisses++;

    let score = 0;
    score += evaluateLines(board); // Rows
    let transposedBoard = transpose(board);
    score += evaluateLines(transposedBoard); // Columns
    let diagonals = getDiagonals(board);
    score += evaluateLines(diagonals); // Diagonals

    // 缓存管理：如果缓存过大，清理一半
    if (evaluationCache.size >= MAX_CACHE_SIZE) {
        const keysToDelete = Array.from(evaluationCache.keys()).slice(0, MAX_CACHE_SIZE / 2);
        keysToDelete.forEach(key => evaluationCache.delete(key));
    }

    evaluationCache.set(boardHash, score);
    return score;
}

// 清理缓存的函数
function clearCaches() {
    evaluationCache.clear();
    cacheHits = 0;
    cacheMisses = 0;
}

// 获取缓存统计信息
function getCacheStats() {
    const total = cacheHits + cacheMisses;
    const hitRate = total > 0 ? (cacheHits / total * 100).toFixed(2) : 0;
    return {
        hits: cacheHits,
        misses: cacheMisses,
        hitRate: hitRate + '%',
        size: evaluationCache.size
    };
}

function evaluateLines(lines) {
    let score = 0;
    for (const line of lines) {
        score += evaluateLine(line);
    }
    return score;
}

function evaluateLine(line) {
    let score = 0;
    // 使用更精确的棋型识别（5个连续位置）
    for (let i = 0; i <= line.length - 5; i++) {
        let window = line.slice(i, i + 5);
        score += scoreWindow(window, 'white') - scoreWindow(window, 'black') * 1.1;
    }

    // 额外检查更长的窗口以识别复杂棋型
    for (let i = 0; i <= line.length - 6; i++) {
        let window = line.slice(i, i + 6);
        score += scoreExtendedWindow(window, 'white') - scoreExtendedWindow(window, 'black') * 1.1;
    }

    return score;
}

function scoreWindow(window, player) {
    let score = 0;
    let playerCount = window.filter(cell => cell === player).length;
    let opponent = player === 'white' ? 'black' : 'white';
    let opponentCount = window.filter(cell => cell === opponent).length;
    let emptyCount = window.filter(cell => cell === null).length;

    // 如果窗口被对手棋子阻挡，则无价值
    if (opponentCount > 0) {
        return 0;
    }

    // 精确的棋型评估
    if (playerCount === 5) {
        score += 100000; // 五连
    } else if (playerCount === 4 && emptyCount === 1) {
        // 检查是否为真正的活四
        if (isLiveFour(window, player)) {
            score += 50000; // 活四
        } else {
            score += 15000; // 冲四
        }
    } else if (playerCount === 3 && emptyCount === 2) {
        // 检查具体的三子棋型
        const patternScore = analyzeThreePattern(window, player);
        score += patternScore;
    } else if (playerCount === 2 && emptyCount === 3) {
        // 检查具体的二子棋型
        const patternScore = analyzeTwoPattern(window, player);
        score += patternScore;
    } else if (playerCount === 1 && emptyCount === 4) {
        score += 10; // 单子
    }

    return score;
}

// 分析三子棋型
function analyzeThreePattern(window, player) {
    const pattern = window.map(cell => cell === player ? 'X' : (cell === null ? '_' : 'O')).join('');

    // 活三模式
    if (pattern === '_XXX_') return 8000; // 标准活三
    if (pattern === 'X_XX_' || pattern === '_XX_X') return 6000; // 跳活三
    if (pattern === 'XX__X' || pattern === 'X__XX') return 4000; // 大跳活三

    // 眠三模式
    if (pattern === 'XXX__' || pattern === '__XXX') return 1500; // 连三
    if (pattern === 'XX_X_' || pattern === '_X_XX') return 1200; // 跳三
    if (pattern === 'X_X_X') return 1000; // 分散三

    return 800; // 其他三子组合
}

// 分析二子棋型
function analyzeTwoPattern(window, player) {
    const pattern = window.map(cell => cell === player ? 'X' : (cell === null ? '_' : 'O')).join('');

    // 活二模式
    if (pattern === '_XX__' || pattern === '__XX_') return 600; // 连二
    if (pattern === '_X_X_') return 500; // 跳二
    if (pattern === 'X___X') return 400; // 大跳二

    // 眠二模式
    if (pattern === 'XX___' || pattern === '___XX') return 200; // 边二
    if (pattern === 'X_X__' || pattern === '__X_X') return 150; // 跳边二

    return 100; // 其他二子组合
}

// 检查是否为特殊棋型组合
function checkSpecialPatterns(row, col, player) {
    let score = 0;

    // 检查双三
    const threeCount = countPatternType(row, col, player, 'three');
    if (threeCount >= 2) {
        score += 20000; // 双三
    }

    // 检查三四组合
    const fourCount = countPatternType(row, col, player, 'four');
    if (fourCount >= 1 && threeCount >= 1) {
        score += 30000; // 三四组合
    }

    // 检查双四
    if (fourCount >= 2) {
        score += 40000; // 双四
    }

    return score;
}

// 计算特定棋型的数量
function countPatternType(row, col, player, patternType) {
    let count = 0;
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        const line = getLineFromPosition(row, col, dx, dy, 9);
        const centerIndex = Math.floor(line.length / 2);

        // 检查5格窗口
        for (let i = Math.max(0, centerIndex - 4); i <= Math.min(line.length - 5, centerIndex); i++) {
            const window = line.slice(i, i + 5);

            if (patternType === 'three') {
                const playerCount = window.filter(cell => cell === player).length;
                const emptyCount = window.filter(cell => cell === null).length;
                const opponent = player === 'white' ? 'black' : 'white';
                const opponentCount = window.filter(cell => cell === opponent).length;

                if (playerCount === 3 && emptyCount === 2 && opponentCount === 0) {
                    if (isLiveThree(window, player)) {
                        count++;
                    }
                }
            } else if (patternType === 'four') {
                const playerCount = window.filter(cell => cell === player).length;
                const emptyCount = window.filter(cell => cell === null).length;
                const opponent = player === 'white' ? 'black' : 'white';
                const opponentCount = window.filter(cell => cell === opponent).length;

                if (playerCount === 4 && emptyCount === 1 && opponentCount === 0) {
                    count++;
                }
            }
        }
    }

    return count;
}

function scoreExtendedWindow(window, player) {
    let score = 0;
    let playerCount = window.filter(cell => cell === player).length;
    let opponent = player === 'white' ? 'black' : 'white';
    let opponentCount = window.filter(cell => cell === opponent).length;

    // 如果窗口被对手棋子阻挡，则无价值
    if (opponentCount > 0) {
        return 0;
    }

    // 检查特殊棋型：跳三、跳四等
    if (playerCount === 4) {
        score += 8000; // 在6格窗口中的四子
    } else if (playerCount === 3) {
        score += 800; // 在6格窗口中的三子
    }

    return score;
}

// 判断是否为活四
function isLiveFour(window, player) {
    const playerCount = window.filter(cell => cell === player).length;
    const emptyCount = window.filter(cell => cell === null).length;

    if (playerCount !== 4 || emptyCount !== 1) return false;

    // 检查四子的连续性和空位位置
    const emptyIndex = window.indexOf(null);

    // 活四：四子连续且空位在两端或中间合适位置
    if (emptyIndex === 0 || emptyIndex === 4) {
        // 空位在两端，检查四子是否连续
        const playerIndices = [];
        for (let i = 0; i < window.length; i++) {
            if (window[i] === player) {
                playerIndices.push(i);
            }
        }
        return playerIndices.length === 4 &&
               playerIndices[3] - playerIndices[0] === 3;
    } else {
        // 空位在中间，检查是否形成活四
        return true;
    }
}

// 判断是否为活三
function isLiveThree(window, player) {
    const playerCount = window.filter(cell => cell === player).length;
    const emptyCount = window.filter(cell => cell === null).length;

    if (playerCount !== 3 || emptyCount !== 2) return false;

    // 检查三子是否有足够的延伸空间
    const firstEmpty = window.indexOf(null);
    const lastEmpty = window.lastIndexOf(null);

    // 活三需要两端都有空位或者中间有空位
    return (firstEmpty === 0 && lastEmpty === 4) ||
           (firstEmpty > 0 && firstEmpty < 4 && lastEmpty === 4) ||
           (firstEmpty === 0 && lastEmpty > 0 && lastEmpty < 4);
}

// 判断是否为活二
function isLiveTwo(window, player) {
    const playerCount = window.filter(cell => cell === player).length;
    const emptyCount = window.filter(cell => cell === null).length;

    if (playerCount !== 2 || emptyCount !== 3) return false;

    // 活二需要有延伸的可能性
    const firstPlayer = window.indexOf(player);
    const lastPlayer = window.lastIndexOf(player);

    return firstPlayer > 0 && lastPlayer < 4;
}

function transpose(matrix) {
    return matrix[0].map((_, i) => matrix.map(row => row[i]));
}

function getDiagonals(matrix) {
    const diagonals = [];
    const n = matrix.length;
    const m = matrix[0].length;

    for (let k = 0; k < n + m - 1; k++) {
        const diag = [];
        for (let j = 0; j <= k; j++) {
            const i = k - j;
            if (i < n && j < m) {
                diag.push(matrix[i][j]);
            }
        }
        if (diag.length >= 5) diagonals.push(diag);
    }

    for (let k = 0; k < n + m - 1; k++) {
        const diag = [];
        for (let j = 0; j <= k; j++) {
            const i = k - j;
            if (i < n && j < m) {
                diag.push(matrix[i][m - 1 - j]);
            }
        }
        if (diag.length >= 5) diagonals.push(diag);
    }
    return diagonals;
}

function checkWin(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        if (count >= 5) {
            return true;
        }
    }
    return false;
}

resetButton.addEventListener('click', initBoard);

initBoard();
