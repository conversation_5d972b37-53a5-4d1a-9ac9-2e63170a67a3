const boardElement = document.getElementById('board');
const resetButton = document.getElementById('reset-button');
const playerTimerElement = document.getElementById('player-timer');
const aiTimerElement = document.getElementById('ai-timer');
const aiThinkingElement = document.getElementById('ai-thinking');
const boardSize = 15;
let board = [];
let currentPlayer = 'black';
let gameOver = false;
let playerTime = 0;
let aiTime = 0;
let playerTimerInterval;
let aiTimerInterval;



// 增强的事件委托绑定
console.log('正在绑定事件监听器...');
boardElement.addEventListener('click', (event) => {
    console.log('棋盘被点击', event.target);
    const cell = event.target.closest('.cell');
    if (cell && cell.dataset.row && cell.dataset.col) {
        console.log('点击了有效单元格:', cell.dataset.row, cell.dataset.col);
        handleCellClick(event);
    } else {
        console.log('点击了非单元格元素', event.target);
    }
});
console.log('事件监听器绑定完成');
function initBoard() {
    console.log('开始初始化棋盘...');
    try {
        board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
        boardElement.innerHTML = '';
        console.log('棋盘数组和DOM已清空');

        // 清空所有缓存
        if (typeof clearCaches === 'function') {
            clearCaches();
            console.log('缓存已清空');
        }

        for (let row = 0; row < boardSize; row++) {
            for (let col = 0; col < boardSize; col++) {
                const cell = document.createElement('div');
                cell.classList.add('cell');
                cell.dataset.row = row;
                cell.dataset.col = col;
                boardElement.appendChild(cell);
            }
        }
        console.log(`已创建 ${boardSize * boardSize} 个单元格`);

        currentPlayer = 'black';
        gameOver = false;
        resetTimers();
        startPlayerTimer();
        console.log('棋盘初始化完成');
    } catch (error) {
        console.error('初始化棋盘时出错:', error);
    }
}

function handleCellClick(event) {
    if (gameOver || currentPlayer !== 'black') return;
    
    const cell = event.target.closest('.cell');
    if (!cell || !cell.dataset.row || !cell.dataset.col) {
        console.error('无效的单元格元素');
        return;
    }
    
    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);
    if (board[row][col]) return;

    stopPlayerTimer();
    board[row][col] = 'black';
    document.querySelector(`[data-row='${row}'][data-col='${col}']`).classList.add('black');

    if (checkWin(row, col, 'black')) {
        gameOver = true;
        setTimeout(() => alert('恭喜你，你赢了!'), 100);
        return;
    }

    currentPlayer = 'white';
    aiMove();
}

async function aiMove() {
    if (gameOver) return;
    aiThinkingElement.style.display = 'block';
    startAiTimer();

    const bestMove = await findBestMoveAsync();

    stopAiTimer();
    aiThinkingElement.style.display = 'none';

    if (bestMove) {
        const { row, col } = bestMove;
        board[row][col] = 'white';
        const cell = document.querySelector(`[data-row='${row}'][data-col='${col}']`);
        cell.classList.add('white');

        // AI赢了直接结束
        if (checkWin(row, col, 'white')) {
            gameOver = true; // 提前设置游戏结束
            // AI棋子闪烁三次，结束后弹窗
            let blinkCount = 0;
            let blinkTimer = setInterval(() => {
                cell.classList.toggle('highlight');
                blinkCount++;
                if (blinkCount >= 6) {
                    clearInterval(blinkTimer);
                    cell.classList.remove('highlight');
                    setTimeout(() => alert('AI 赢了!'), 100);
                }
            }, 200);
            return;
        }

        // AI棋子闪烁三次，结束后切换到玩家回合
        let blinkCount = 0;
        let blinkTimer = setInterval(() => {
            cell.classList.toggle('highlight');
            blinkCount++;
            if (blinkCount >= 6) {
                clearInterval(blinkTimer);
                cell.classList.remove('highlight');
                // 闪烁结束后才允许玩家落子
                currentPlayer = 'black';
                startPlayerTimer();
            }
        }, 200);
    } else {
        // 检查是否棋盘已满
        const isBoardFull = board.flat().every(cell => cell !== null);
        if (isBoardFull) {
            gameOver = true;
            setTimeout(() => alert('平局!'), 100);
        } else {
            // 如果AI找不到移动但棋盘未满，继续玩家回合
            currentPlayer = 'black';
            startPlayerTimer();
        }
        return;
    }
}

function resetTimers() {
    clearInterval(playerTimerInterval);
    clearInterval(aiTimerInterval);
    playerTimerInterval = null;
    aiTimerInterval = null;
    playerTime = 0;
    aiTime = 0;
    playerTimerElement.textContent = '玩家用时: 0s';
    aiTimerElement.textContent = 'AI用时: 0s';
}

function startPlayerTimer() {
    clearInterval(playerTimerInterval);
    playerTimerInterval = setInterval(() => {
        playerTime++;
        playerTimerElement.textContent = `玩家用时: ${playerTime}s`;
    }, 1000);
}

function stopPlayerTimer() {
    clearInterval(playerTimerInterval);
}

function startAiTimer() {
    clearInterval(aiTimerInterval);
    aiTimerInterval = setInterval(() => {
        aiTime++;
        aiTimerElement.textContent = `AI用时: ${aiTime}s`;
    }, 1000);
}

function stopAiTimer() {
    clearInterval(aiTimerInterval);
}

async function findBestMoveAsync() {
    let pieceCount = 0;
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c]) {
                pieceCount++;
            }
        }
    }

    // 第一步AI移动直接返回固定开局位置
    if (pieceCount === 1) {
        console.log('完全跳过所有计算（第一步直接使用固定开局）');
        const center = Math.floor(boardSize / 2);
        // 使用星位开局（中心-3,-3）
        const move = {row: center-3, col: center-3};
        // 确保位置有效
        if (isValidPosition(move.row, move.col) && board[move.row][move.col] === null) {
            return move;
        }
        // 回退到备用位置
        return {row: center, col: center};
    }

    // 首先检查是否有立即获胜的机会（最高优先级）
    const winningMove = findWinningMove('white');
    if (winningMove) {
        console.log('AI发现获胜机会:', winningMove);
        return winningMove;
    }

    // 立即检查防守
    const blockingMove = findWinningMove('black');
    if (blockingMove) {
        console.log('🛡️ AI防守:', blockingMove);
        return blockingMove;
    }

    // 超简单位置选择 - 找第一个合适的空位
    const center = Math.floor(boardSize / 2);

    // 找已有棋子附近的位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                // 检查周围8个位置
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue;
                        const newRow = row + dr;
                        const newCol = col + dc;

                        if (newRow >= 0 && newRow < boardSize &&
                            newCol >= 0 && newCol < boardSize &&
                            board[newRow][newCol] === null) {
                            console.log(`⚡ AI选择邻近位置: (${newRow}, ${newCol})`);
                            return { row: newRow, col: newCol };
                        }
                    }
                }
            }
        }
    }

    // 如果没有邻近位置，使用中心区域
    const priorityPositions = [
        {row: center, col: center},
        {row: center-1, col: center-1}, {row: center-1, col: center}, {row: center-1, col: center+1},
        {row: center, col: center-1}, {row: center, col: center+1},
        {row: center+1, col: center-1}, {row: center+1, col: center}, {row: center+1, col: center+1}
    ];

    for (const pos of priorityPositions) {
        if (pos.row >= 0 && pos.row < boardSize &&
            pos.col >= 0 && pos.col < boardSize &&
            board[pos.row][pos.col] === null) {
            console.log(`⚡ AI选择中心位置: (${pos.row}, ${pos.col})`);
            return pos;
        }
    }

    // 最后的备选：找任意空位
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === null) {
                console.log(`⚡ AI选择任意位置: (${row}, ${col})`);
                return { row, col };
            }
        }
    }

    console.log('❌ 没有可用位置');
    return null;
}

// 超快速搜索 - 解决响应时间问题
async function quickSearch(_) {
    console.log('🚀 启动快速搜索...');
    const startTime = Date.now();

    // 获取候选位置
    const candidates = getSimpleCandidates();
    if (candidates.length === 0) {
        // 找第一个空位
        for (let row = 0; row < boardSize; row++) {
            for (let col = 0; col < boardSize; col++) {
                if (board[row][col] === null) {
                    console.log(`⚡ 紧急位置: (${row}, ${col})`);
                    return { row, col };
                }
            }
        }
    }

    // 快速评估前5个位置
    let bestMove = candidates[0];
    let bestScore = -Infinity;

    for (let i = 0; i < Math.min(5, candidates.length); i++) {
        const move = candidates[i];
        board[move.row][move.col] = 'white';
        const score = quickEvaluate();
        board[move.row][move.col] = null;

        if (score > bestScore) {
            bestScore = score;
            bestMove = move;
        }

        // 时间限制：300ms
        if (Date.now() - startTime > 300) {
            console.log('⏰ 时间限制，提前结束搜索');
            break;
        }

        // 每个位置后yield
        await new Promise(resolve => setTimeout(resolve, 0));
    }

    console.log(`✅ 快速搜索完成: (${bestMove.row}, ${bestMove.col}) - 用时: ${Date.now() - startTime}ms`);
    return bestMove;
}

// 获取简单候选位置
function getSimpleCandidates() {
    const candidates = [];
    const center = Math.floor(boardSize / 2);

    // 只搜索已有棋子周围1格的位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                // 检查周围8个位置
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue;
                        const newRow = row + dr;
                        const newCol = col + dc;

                        if (isValidPosition(newRow, newCol) &&
                            board[newRow][newCol] === null &&
                            !candidates.some(c => c.row === newRow && c.col === newCol)) {
                            candidates.push({ row: newRow, col: newCol });
                        }
                    }
                }
            }
        }
    }

    // 如果没有候选位置，添加中心区域
    if (candidates.length === 0) {
        for (let dr = -2; dr <= 2; dr++) {
            for (let dc = -2; dc <= 2; dc++) {
                const row = center + dr;
                const col = center + dc;
                if (isValidPosition(row, col) && board[row][col] === null) {
                    candidates.push({ row, col });
                }
            }
        }
    }

    return candidates.slice(0, 10); // 最多10个候选位置
}

// 快速评估函数
function quickEvaluate() {
    let score = 0;
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    // 只检查关键位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === 'white') {
                for (const [dx, dy] of directions) {
                    const count = countConsecutive(row, col, dx, dy, 'white');
                    if (count >= 4) score += 10000;
                    else if (count === 3) score += 1000;
                    else if (count === 2) score += 100;
                }
            } else if (board[row][col] === 'black') {
                for (const [dx, dy] of directions) {
                    const count = countConsecutive(row, col, dx, dy, 'black');
                    if (count >= 4) score -= 10000;
                    else if (count === 3) score -= 1000;
                    else if (count === 2) score -= 100;
                }
            }
        }
    }

    return score;
}

// 优化迭代加深搜索
async function iterativeDeepeningSearch(pieceCount) {
    let bestMove = null;
    let bestScore = -Infinity;
    const historyTable = Array(boardSize).fill(null).map(() => Array(boardSize).fill(0));
    
    // 动态调整搜索参数 - 优化响应速度
    const maxDepth = calculateDynamicDepth(pieceCount);
    const timeLimit = pieceCount < 8 ? 800 : 600; // 大幅减少搜索时间
    const startTime = Date.now();

    // 获取排序后的候选位置 - 减少候选数量
    const candidateMoves = getSortedCandidateMoves();
    const maxCandidates = Math.min(candidateMoves.length, pieceCount < 8 ? 12 : 8);

    // 置换表用于存储搜索结果
    const transpositionTable = new Map();

    // 从深度2开始迭代加深
    for (let depth = 2; depth <= maxDepth; depth++) {
        let currentBestMove = null;
        let currentBestScore = -Infinity;
        
        // 按历史启发排序候选移动
        candidateMoves.sort((a, b) => {
            return historyTable[b.row][b.col] - historyTable[a.row][a.col];
        });

        for (let i = 0; i < maxCandidates; i++) {
            // 更频繁的时间检查 - 每3个位置检查一次
            if (i % 3 === 0 && Date.now() - startTime > timeLimit) break;
            
            const move = candidateMoves[i];
            board[move.row][move.col] = 'white';
            
            // 空步裁剪：允许跳过对手回合
            let score;
            if (depth >= 3 && pieceCount > 6) {
                board[move.row][move.col] = null;
                score = -negamax(depth - 3, -Infinity, -bestScore, false, transpositionTable, historyTable);
                board[move.row][move.col] = 'white';
            } else {
                score = minimaxWithTT(depth - 1, false, -Infinity, Infinity, transpositionTable);
            }
            
            board[move.row][move.col] = null;
            
            // 更新历史表
            if (score > currentBestScore) {
                currentBestMove = move;
                currentBestScore = score;
                historyTable[move.row][move.col] += depth * depth;
            }

            // 使用更高效的异步处理
            if (i % 3 === 0) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
        }

        if (currentBestMove && currentBestScore > bestScore) {
            bestMove = currentBestMove;
            bestScore = currentBestScore;
        }
        
        // 必胜局面提前终止
        if (bestScore > 100000) break;
        if (Date.now() - startTime > timeLimit) break;
    }
    
    return bestMove;
}

// 优化搜索深度计算 - 平衡棋力和速度
function calculateDynamicDepth(pieceCount) {
    if (pieceCount < 6) return 2;      // 开局：深度2，快速响应
    if (pieceCount < 12) return 3;     // 中局前期：深度3
    if (pieceCount < 25) return 2;     // 中局后期：深度2，加快速度
    return 2;                          // 残局：深度2
}

// 负极大算法实现
function negamax(depth, alpha, beta, isMaximizing, transpositionTable, _) {
    return minimaxWithTT(depth, isMaximizing, alpha, beta, transpositionTable);
}

// 改进的开局策略
// 增强的开局库
function getOpeningMove(pieceCount) {
    const center = Math.floor(boardSize / 2);
    const playerMoves = getPlayerMoves();
    
        // 优化开局库：添加默认条件
        const openings = {
            // 第一步：中心点
            0: [{ move: {row: center, col: center}, name: "天元开局" }],
            
            // 第二步：根据玩家第一步响应
            1: [
                // 玩家下天元
                { condition: (moves) => moves[0].row === center && moves[0].col === center,
                  moves: [
                    {row: center-2, col: center-2, name: "星位开局"},
                    {row: center-3, col: center, name: "小目开局"}
                  ]},
                // 玩家下星位
                { condition: (moves) => Math.abs(moves[0].row - center) <= 2 && 
                                       Math.abs(moves[0].col - center) <= 2,
                  moves: [
                    {row: 2*center - moves[0].row, col: 2*center - moves[0].col, name: "对称开局"},
                    {row: center, col: center, name: "占据天元"}
                  ]},
                // 默认：玩家下边角或其他位置
                { condition: () => true,   // 始终匹配
                  moves: [
                    {row: center-3, col: center-3, name: "星位开局 (default)"},
                    {row: center-3, col: center+3, name: "星位开局 (default)"},
                    {row: center+3, col: center-3, name: "星位开局 (default)"},
                    {row: center+3, col: center+3, name: "星位开局 (default)"}
                  ]}
            ],
        
        // 第三步：形成专业开局
        2: [
            // 识别常见开局
            { condition: (moves) => moves.some(m => m.row === center && m.col === center),
              moves: [
                {row: center+2, col: center-2, name: "二连星"},
                {row: center-2, col: center+2, name: "错向小目"}
              ]},
            // 对抗性开局
            { condition: (moves) => moves[0].row === moves[1].row,
              moves: [
                {row: moves[0].row, col: center, name: "横向阻断"},
                {row: center, col: moves[0].col, name: "纵向切入"}
              ]}
        ]
    };
    
    // 优化开局选择：确保快速响应
    if (openings[pieceCount]) {
        const center = Math.floor(boardSize / 2);
        
        // 对于第一步AI移动，使用预定义响应
        if (pieceCount === 1) {
            const playerMove = playerMoves[0];
            const dx = playerMove.row - center;
            const dy = playerMove.col - center;
            
            // 根据玩家位置选择最佳响应
            let response;
            if (Math.abs(dx) <= 2 && Math.abs(dy) <= 2) { // 中心区域
                response = {row: center-2, col: center-2, name: "星位开局"};
            } else { // 边角区域
                response = {row: center+3, col: center+3, name: "星位开局 (default)"};
            }
            
            // 确保位置有效
            if (isValidPosition(response.row, response.col) && board[response.row][response.col] === null) {
                console.log(`使用优化开局: ${response.name} (${response.row},${response.col})`);
                return response;
            }
        }
        
        // 通用开局逻辑
        const openingSet = openings[pieceCount];
        const matchedOpening = openingSet.find(opening => opening.condition(playerMoves));
        if (matchedOpening) {
            for (const move of matchedOpening.moves) {
                if (isValidPosition(move.row, move.col) && board[move.row][move.col] === null) {
                    console.log(`使用开局: ${move.name} (${move.row},${move.col})`);
                    return {row: move.row, col: move.col};
                }
            }
        }
        
        // 快速回退：直接返回第一个空位
        for (let r = 0; r < boardSize; r++) {
            for (let c = 0; c < boardSize; c++) {
                if (board[r][c] === null) {
                    console.log(`使用快速回退开局: (${r},${c})`);
                    return {row: r, col: c};
                }
            }
        }
    }
    
    // 如果所有尝试都失败，返回null（虽然理论上不会发生）
    console.error('所有开局策略失败，返回null');
    return null;
}

// 优化：高效获取玩家落子位置
function getPlayerMoves() {
    // 对于第一步，直接返回第一个黑棋位置
    if (pieceCount === 1) {
        for (let r = 0; r < boardSize; r++) {
            for (let c = 0; c < boardSize; c++) {
                if (board[r][c] === 'black') {
                    return [{ row: r, col: c }];
                }
            }
        }
    }
    
    // 对于其他情况，收集所有黑棋位置
    const moves = [];
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c] === 'black') {
                moves.push({ row: r, col: c });
            }
        }
    }
    return moves;
}

// AI第二步策略
function getSecondMove(playerFirstMove, center) {
    const dx = playerFirstMove.row - center;
    const dy = playerFirstMove.col - center;

    // 根据玩家第一步的位置选择不同的开局
    if (dx === 0 && dy === 0) {
        // 玩家下在中心，AI选择天元开局
        return { row: center - 1, col: center - 1 };
    } else if (Math.abs(dx) <= 1 && Math.abs(dy) <= 1) {
        // 玩家在中心附近，使用星月开局
        return getStarMoonOpening(playerFirstMove, center);
    } else {
        // 玩家远离中心，使用花月开局
        return getFlowerMoonOpening(playerFirstMove, center);
    }
}

// 星月开局
function getStarMoonOpening(playerMove, center) {
    const candidates = [
        { row: center - 1, col: center - 1 },
        { row: center - 1, col: center + 1 },
        { row: center + 1, col: center - 1 },
        { row: center + 1, col: center + 1 },
    ];

    // 选择与玩家棋子形成对角的位置
    for (const candidate of candidates) {
        if (board[candidate.row][candidate.col] === null) {
            const distance = Math.abs(candidate.row - playerMove.row) + Math.abs(candidate.col - playerMove.col);
            if (distance === 2) {
                return candidate;
            }
        }
    }

    // 如果没有合适的对角位置，选择第一个可用位置
    return candidates.find(pos => board[pos.row][pos.col] === null);
}

// 花月开局
function getFlowerMoonOpening(playerMove, center) {
    // 在中心附近选择一个平衡的位置，避开玩家棋子方向
    const candidates = [
        { row: center, col: center - 1 },
        { row: center, col: center + 1 },
        { row: center - 1, col: center },
        { row: center + 1, col: center },
    ];

    // 优先选择远离玩家棋子的位置
    candidates.sort((a, b) => {
        const distA = Math.abs(a.row - playerMove.row) + Math.abs(a.col - playerMove.col);
        const distB = Math.abs(b.row - playerMove.row) + Math.abs(b.col - playerMove.col);
        return distB - distA;
    });

    return candidates.find(pos => board[pos.row][pos.col] === null) || candidates[0];
}

// AI第三步策略
function getThirdMove(playerMoves, center) {
    if (playerMoves.length < 2) return null;

    // 分析玩家的布局意图
    const pattern = analyzePlayerPattern(playerMoves, center);

    switch (pattern) {
        case 'aggressive':
            return getDefensiveThirdMove(playerMoves, center);
        case 'balanced':
            return getBalancedThirdMove(playerMoves, center);
        case 'defensive':
            return getAggressiveThirdMove(playerMoves, center);
        default:
            return getDefaultThirdMove(center);
    }
}

// AI第四步策略
function getFourthMove(playerMoves, center) {
    // 在第四步开始更多依赖评估函数
    const candidates = getSortedCandidateMoves();

    // 但仍然考虑一些开局原则和玩家位置
    const filteredCandidates = candidates.filter(move => {
        const distance = Math.abs(move.row - center) + Math.abs(move.col - center);

        // 避免过于靠近玩家密集区域
        let tooClose = false;
        for (const playerMove of playerMoves) {
            const playerDistance = Math.abs(move.row - playerMove.row) + Math.abs(move.col - playerMove.col);
            if (playerDistance === 1) {
                tooClose = true;
                break;
            }
        }

        return distance <= 4 && !tooClose; // 保持在中心区域且不太靠近玩家
    });

    return filteredCandidates.length > 0 ? filteredCandidates[0] : candidates[0];
}

// AI第五步策略
function getFifthMove() {
    // 第五步完全依赖评估函数，但优先考虑攻击性位置
    const candidates = getSortedCandidateMoves();

    // 寻找能形成威胁的位置
    for (const candidate of candidates.slice(0, 5)) {
        board[candidate.row][candidate.col] = 'white';
        const threats = analyzePositionThreats(candidate.row, candidate.col, 'white');
        board[candidate.row][candidate.col] = null;

        if (threats.activeThrees > 0 || threats.rushFours > 0) {
            return candidate;
        }
    }

    return candidates[0];
}

// 分析玩家开局模式
function analyzePlayerPattern(playerMoves, center) {
    if (playerMoves.length < 2) return 'balanced';

    let aggressiveScore = 0;
    let defensiveScore = 0;

    for (const move of playerMoves) {
        const distance = Math.abs(move.row - center) + Math.abs(move.col - center);

        if (distance <= 2) {
            aggressiveScore += 2; // 靠近中心，较为激进
        } else {
            defensiveScore += 1; // 远离中心，较为保守
        }
    }

    if (aggressiveScore > defensiveScore * 1.5) {
        return 'aggressive';
    } else if (defensiveScore > aggressiveScore * 1.5) {
        return 'defensive';
    } else {
        return 'balanced';
    }
}

// 防守型第三步
function getDefensiveThirdMove(playerMoves, center) {
    // 在玩家棋子之间插入，破坏其连接
    const candidates = [];

    for (let i = 0; i < playerMoves.length; i++) {
        for (let j = i + 1; j < playerMoves.length; j++) {
            const midRow = Math.floor((playerMoves[i].row + playerMoves[j].row) / 2);
            const midCol = Math.floor((playerMoves[i].col + playerMoves[j].col) / 2);

            if (isValidPosition(midRow, midCol) && board[midRow][midCol] === null) {
                candidates.push({ row: midRow, col: midCol });
            }
        }
    }

    return candidates.length > 0 ? candidates[0] : getDefaultThirdMove(center);
}

// 平衡型第三步
function getBalancedThirdMove(_, center) {
    // 选择一个既不太激进也不太保守的位置
    const candidates = [
        { row: center - 2, col: center },
        { row: center + 2, col: center },
        { row: center, col: center - 2 },
        { row: center, col: center + 2 },
    ];

    return candidates.find(pos => board[pos.row][pos.col] === null) || getDefaultThirdMove(center);
}

// 攻击型第三步
function getAggressiveThirdMove(_, center) {
    // 选择一个能形成攻击的位置
    const candidates = getSortedCandidateMoves();

    for (const candidate of candidates.slice(0, 3)) {
        board[candidate.row][candidate.col] = 'white';
        const score = evaluateBoard();
        board[candidate.row][candidate.col] = null;

        if (score > 1000) { // 如果能形成较好的棋型
            return candidate;
        }
    }

    return getDefaultThirdMove(center);
}

// 默认第三步
function getDefaultThirdMove(center) {
    const candidates = [
        { row: center - 1, col: center - 2 },
        { row: center - 1, col: center + 2 },
        { row: center + 1, col: center - 2 },
        { row: center + 1, col: center + 2 },
    ];

    return candidates.find(pos => isValidPosition(pos.row, pos.col) && board[pos.row][pos.col] === null) ||
           { row: center - 2, col: center - 2 };
}

// 寻找获胜位置
function findWinningMove(player) {
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (!board[row][col]) {
                board[row][col] = player;
                if (checkWin(row, col, player)) {
                    board[row][col] = null;
                    return { row, col };
                }
                board[row][col] = null;
            }
        }
    }
    return null;
}

// 增强的威胁检测系统
function findDoubleThreatMove() {
    // 寻找能创造多重威胁的位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (!board[row][col]) {
                board[row][col] = 'white';
                const newThreats = analyzePositionThreats(row, col, 'white');
                board[row][col] = null;

                // 如果能创造双威胁或更多
                if (newThreats.activeFours >= 2 ||
                    (newThreats.activeFours >= 1 && newThreats.activeThrees >= 1) ||
                    newThreats.activeThrees >= 2) {
                    return { row, col };
                }
            }
        }
    }
    return null;
}

// 分析所有威胁
function analyzeAllThreats() {
    const threats = {
        white: { activeFours: [], activeThrees: [], rushFours: [] },
        black: { activeFours: [], activeThrees: [], rushFours: [] }
    };

    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (!board[row][col]) {
                // 分析白棋威胁
                board[row][col] = 'white';
                const whiteThreat = analyzePositionThreats(row, col, 'white');
                if (whiteThreat.activeFours > 0) threats.white.activeFours.push({row, col});
                if (whiteThreat.activeThrees > 0) threats.white.activeThrees.push({row, col});
                if (whiteThreat.rushFours > 0) threats.white.rushFours.push({row, col});
                board[row][col] = null;

                // 分析黑棋威胁
                board[row][col] = 'black';
                const blackThreat = analyzePositionThreats(row, col, 'black');
                if (blackThreat.activeFours > 0) threats.black.activeFours.push({row, col});
                if (blackThreat.activeThrees > 0) threats.black.activeThrees.push({row, col});
                if (blackThreat.rushFours > 0) threats.black.rushFours.push({row, col});
                board[row][col] = null;
            }
        }
    }

    return threats;
}

// 分析单个位置的威胁
function analyzePositionThreats(row, col, player) {
    const threats = { activeFours: 0, activeThrees: 0, rushFours: 0 };
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        const line = getLineFromPosition(row, col, dx, dy, 9);
        const centerIndex = Math.floor(line.length / 2);

        // 检查5格窗口
        for (let i = Math.max(0, centerIndex - 4); i <= Math.min(line.length - 5, centerIndex); i++) {
            const window = line.slice(i, i + 5);
            const playerCount = window.filter(cell => cell === player).length;
            const emptyCount = window.filter(cell => cell === null).length;
            const opponent = player === 'white' ? 'black' : 'white';
            const opponentCount = window.filter(cell => cell === opponent).length;

            if (opponentCount === 0) {
                if (playerCount === 4 && emptyCount === 1) {
                    if (isLiveFour(window, player)) {
                        threats.activeFours++;
                    } else {
                        threats.rushFours++;
                    }
                } else if (playerCount === 3 && emptyCount === 2) {
                    if (isLiveThree(window, player)) {
                        threats.activeThrees++;
                    }
                }
            }
        }
    }

    return threats;
}

// 获取从指定位置延伸的直线
function getLineFromPosition(row, col, dx, dy, length) {
    const line = [];
    const halfLength = Math.floor(length / 2);

    for (let i = -halfLength; i <= halfLength; i++) {
        const newRow = row + i * dx;
        const newCol = col + i * dy;

        if (isValidPosition(newRow, newCol)) {
            line.push(board[newRow][newCol]);
        } else {
            line.push('boundary'); // 边界标记
        }
    }

    return line;
}

// 寻找最紧急的防守位置
function findUrgentDefense() {
    // 首先检查对手是否有立即获胜的威胁
    const immediateWin = findWinningMove('black');
    if (immediateWin) {
        return immediateWin;
    }

    // 检查对手的活四威胁
    const activeFours = findExistingThreats('black', 4);
    if (activeFours.length > 0) {
        return activeFours[0];
    }

    // 检查对手的活三威胁
    const activeThrees = findExistingThreats('black', 3);
    if (activeThrees.length > 0) {
        // 如果有多个活三，寻找能同时防守的位置
        if (activeThrees.length >= 2) {
            const multiDefense = findBestDefensePosition(activeThrees);
            if (multiDefense) {
                return multiDefense;
            }
        }
        return activeThrees[0];
    }

    // 检查对手的冲四威胁
    const rushFours = findExistingRushFours('black');
    if (rushFours.length > 0) {
        return rushFours[0];
    }

    return null;
}

// 寻找已存在的威胁（活三、活四等）
function findExistingThreats(player, count) {
    const threats = [];
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === player) {
                // 从每个玩家棋子开始检查四个方向
                for (const [dx, dy] of directions) {
                    const threatPositions = findThreatInDirection(row, col, dx, dy, player, count);
                    threats.push(...threatPositions);
                }
            }
        }
    }

    // 去重
    return removeDuplicatePositions(threats);
}

// 在特定方向寻找威胁
function findThreatInDirection(startRow, startCol, dx, dy, player, targetCount) {
    const threats = [];

    // 检查从当前位置开始的5格窗口
    for (let offset = -4; offset <= 0; offset++) {
        const windowStart = {
            row: startRow + offset * dx,
            col: startCol + offset * dy
        };

        // 构建5格窗口
        const window = [];
        const positions = [];

        for (let i = 0; i < 5; i++) {
            const pos = {
                row: windowStart.row + i * dx,
                col: windowStart.col + i * dy
            };

            if (isValidPosition(pos.row, pos.col)) {
                window.push(board[pos.row][pos.col]);
                positions.push(pos);
            } else {
                window.push('boundary');
                positions.push(null);
            }
        }

        // 检查这个窗口是否包含威胁
        const playerCount = window.filter(cell => cell === player).length;
        const emptyCount = window.filter(cell => cell === null).length;
        const opponent = player === 'white' ? 'black' : 'white';
        const opponentCount = window.filter(cell => cell === opponent).length;

        // 如果没有对手棋子阻挡，且棋子数量匹配
        if (opponentCount === 0 && playerCount === targetCount && emptyCount === 5 - targetCount) {
            // 检查是否为活威胁
            if (targetCount === 3 && isLiveThree(window, player)) {
                // 找到空位作为防守点
                for (let i = 0; i < 5; i++) {
                    if (window[i] === null && positions[i]) {
                        threats.push(positions[i]);
                    }
                }
            } else if (targetCount === 4 && isLiveFour(window, player)) {
                // 找到空位作为防守点
                for (let i = 0; i < 5; i++) {
                    if (window[i] === null && positions[i]) {
                        threats.push(positions[i]);
                    }
                }
            }
        }
    }

    return threats;
}

// 寻找冲四威胁
function findExistingRushFours(player) {
    const threats = [];
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === player) {
                for (const [dx, dy] of directions) {
                    // 检查是否能形成冲四
                    const consecutiveCount = countConsecutive(row, col, dx, dy, player);
                    if (consecutiveCount === 4) {
                        // 寻找能阻挡的位置
                        const blockPositions = findBlockPositions(row, col, dx, dy, player);
                        threats.push(...blockPositions);
                    }
                }
            }
        }
    }

    return removeDuplicatePositions(threats);
}

// 寻找阻挡位置
function findBlockPositions(row, col, dx, dy, player) {
    const positions = [];

    // 检查两个方向的延伸
    for (let direction = -1; direction <= 1; direction += 2) {
        for (let i = 1; i <= 4; i++) {
            const newRow = row + i * dx * direction;
            const newCol = col + i * dy * direction;

            if (isValidPosition(newRow, newCol)) {
                if (board[newRow][newCol] === null) {
                    positions.push({ row: newRow, col: newCol });
                    break; // 只需要第一个空位
                } else if (board[newRow][newCol] !== player) {
                    break; // 被对手棋子阻挡
                }
            } else {
                break; // 到达边界
            }
        }
    }

    return positions;
}

// 寻找最佳防守位置（能防守多个威胁）
function findBestDefensePosition(threats) {
    const defenseMap = new Map();

    // 统计每个位置能防守多少个威胁
    for (const threat of threats) {
        const key = `${threat.row},${threat.col}`;
        defenseMap.set(key, (defenseMap.get(key) || 0) + 1);
    }

    // 找到能防守最多威胁的位置
    let bestPosition = null;
    let maxDefenseCount = 0;

    for (const [key, count] of defenseMap) {
        if (count > maxDefenseCount) {
            maxDefenseCount = count;
            const [row, col] = key.split(',').map(Number);
            bestPosition = { row, col };
        }
    }

    return bestPosition;
}

// 去除重复位置
function removeDuplicatePositions(positions) {
    const unique = [];
    const seen = new Set();

    for (const pos of positions) {
        const key = `${pos.row},${pos.col}`;
        if (!seen.has(key)) {
            seen.add(key);
            unique.push(pos);
        }
    }

    return unique;
}

// 直接扫描棋盘寻找威胁（更简单直接的方法）
function findDirectThreats(player) {
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    // 扫描整个棋盘
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === null) {
                // 在每个空位尝试放置对手棋子，看是否形成威胁
                board[row][col] = player;

                // 检查是否形成活三或活四
                for (const [dx, dy] of directions) {
                    const count = countConsecutive(row, col, dx, dy, player);

                    if (count >= 3) {
                        // 检查是否为活三或活四
                        const isLive = checkIfLiveThreat(row, col, dx, dy, player, count);
                        if (isLive) {
                            board[row][col] = null;
                            return { row, col }; // 立即返回防守位置
                        }
                    }
                }

                board[row][col] = null;
            }
        }
    }

    return null;
}

// 检查是否为活威胁
function checkIfLiveThreat(row, col, dx, dy, _, count) {
    if (count < 3) return false;

    // 检查两端是否有空位
    let frontEmpty = false;
    let backEmpty = false;

    // 检查前方
    const frontRow = row + dx;
    const frontCol = col + dy;
    if (isValidPosition(frontRow, frontCol) && board[frontRow][frontCol] === null) {
        frontEmpty = true;
    }

    // 检查后方
    const backRow = row - dx;
    const backCol = col - dy;
    if (isValidPosition(backRow, backCol) && board[backRow][backCol] === null) {
        backEmpty = true;
    }

    // 活威胁需要至少一端有空位
    if (count === 3) {
        // 活三需要两端都有空位，或者一端有空位且能延伸
        return frontEmpty || backEmpty;
    } else if (count === 4) {
        // 活四只需要一端有空位
        return frontEmpty || backEmpty;
    }

    return false;
}

// 寻找能防守多个威胁的位置
function findMultiDefensePosition(threats) {
    const defenseMap = new Map();

    for (const threat of threats) {
        // 分析每个威胁周围的防守点
        const defensePoints = getDefensePoints(threat.row, threat.col);
        for (const point of defensePoints) {
            const key = `${point.row},${point.col}`;
            defenseMap.set(key, (defenseMap.get(key) || 0) + 1);
        }
    }

    // 找到能防守最多威胁的位置
    let bestDefense = null;
    let maxDefenseCount = 0;

    for (const [key, count] of defenseMap) {
        if (count > maxDefenseCount) {
            maxDefenseCount = count;
            const [row, col] = key.split(',').map(Number);
            bestDefense = { row, col };
        }
    }

    return bestDefense;
}

// 获取威胁的防守点
function getDefensePoints(row, col) {
    const defensePoints = [];
    const searchRange = 2;

    for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
        for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
            if (!board[r][c]) {
                defensePoints.push({ row: r, col: c });
            }
        }
    }

    return defensePoints;
}

// 优化候选位置获取
function getSortedCandidateMoves() {
    const candidateMoves = [];
    const searchRange = 2;

    // 收集候选位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
                    for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
                        if (!board[r][c] && !candidateMoves.some(m => m.row === r && m.col === c)) {
                            candidateMoves.push({ row: r, col: c });
                        }
                    }
                }
            }
        }
    }

    // 如果没有候选位置，使用中心区域
    if (candidateMoves.length === 0) {
        const center = Math.floor(boardSize / 2);
        candidateMoves.push({ row: center, col: center });
    }

    // 优化：限制候选位置数量
    const MAX_CANDIDATES = 25;
    if (candidateMoves.length > MAX_CANDIDATES) {
        candidateMoves.length = MAX_CANDIDATES;
    }

    return candidateMoves;
}

// 评估单个位置的价值
// 增强的全局位置评估系统
function evaluatePosition(row, col) {
    let score = 0;
    const center = Math.floor(boardSize / 2);

    // === 1. 基础进攻价值 ===
    board[row][col] = 'white';
    score += evaluateBoard() * 1.2;  // 进攻权重提高
    board[row][col] = null;

    // === 2. 防守价值评估 ===
    board[row][col] = 'black';
    score -= evaluateBoard() * 1.3;  // 防守权重更高
    board[row][col] = null;

    // === 3. 战略位置价值 ===
    // 中心控制 (天元及星位高价值)
    const centerDist = Math.abs(row - center) + Math.abs(col - center);
    score += (boardSize - centerDist) * 25; 

    // === 4. 影响区域分析 ===
    score += calculateInfluence(row, col, 'white') * 1.5;
    score -= calculateInfluence(row, col, 'black') * 1.2;

    // === 5. 连接潜力 ===
    score += analyzeConnectionPotential(row, col);

    // === 6. 特殊模式奖励 ===
    score += checkAdvancedPatterns(row, col, 'white') * 2;

    return Math.round(score * 100) / 100; // 保留两位小数
}

// 新增：计算位置影响力
function calculateInfluence(row, col, player) {
    let influence = 0;
    const directions = [[1,0],[0,1],[1,1],[1,-1],[-1,0],[0,-1],[-1,-1],[-1,1]];
    
    for (const [dx, dy] of directions) {
        for (let dist = 1; dist <= 3; dist++) {
            const r = row + dx * dist;
            const c = col + dy * dist;
            if (isValidPosition(r, c)) {
                if (board[r][c] === player) influence += 40 / dist;
                else if (board[r][c] === null) influence += 20 / dist;
            }
        }
    }
    return influence;
}

// 新增：高级模式识别
function checkAdvancedPatterns(row, col, player) {
    let bonus = 0;
    const patterns = [
        { name: '双三', test: () => countPatternType(row, col, player, 'three') >= 2, score: 3000 },
        { name: '三四', test: () => countPatternType(row, col, player, 'four') >= 1 && 
                                 countPatternType(row, col, player, 'three') >= 1, score: 5000 },
        { name: '双四', test: () => countPatternType(row, col, player, 'four') >= 2, score: 8000 },
        { name: '活三+冲四', test: function() {
            return findThreatInDirection(row, col, 1, 0, player, 3) && 
                   findThreatInDirection(row, col, 0, 1, player, 4);
        }, score: 6000 }
    ];

    for (const pattern of patterns) {
        if (pattern.test()) {
            console.log(`识别高级模式: ${pattern.name} 在 (${row},${col})`);
            bonus += pattern.score;
        }
    }
    return bonus;
}

// 新增：连接潜力分析
function analyzeConnectionPotential(row, col) {
    let potential = 0;
    const friendStones = [];
    
    // 扫描8方向寻找友方棋子
    for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
            if (dx === 0 && dy === 0) continue;
            for (let dist = 1; dist <= 4; dist++) {
                const r = row + dx * dist;
                const c = col + dy * dist;
                if (!isValidPosition(r, c)) break;
                if (board[r][c] === 'white') {
                    friendStones.push({row: r, col: c, dist});
                    break;
                }
            }
        }
    }
    
    // 计算连接价值
    if (friendStones.length >= 2) {
        potential = friendStones.reduce((sum, stone) => sum + 100 / stone.dist, 0);
    }
    
    return potential;
}

// 计算位置权重
function calculatePositionWeight(row, col) {
    let weight = 0;
    const center = Math.floor(boardSize / 2);

    // 中心区域权重
    const distanceFromCenter = Math.abs(row - center) + Math.abs(col - center);
    weight += (boardSize - distanceFromCenter) * 15;

    // 边角惩罚
    if (row === 0 || row === boardSize - 1 || col === 0 || col === boardSize - 1) {
        weight -= 50;
    }

    // 次边惩罚
    if (row === 1 || row === boardSize - 2 || col === 1 || col === boardSize - 2) {
        weight -= 20;
    }

    return weight;
}

// 分析连接性
function analyzeConnectivity(row, col) {
    let connectivity = 0;
    const searchRange = 3;

    // 检查周围已有棋子的连接潜力
    for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
        for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
            if (board[r][c]) {
                const distance = Math.abs(r - row) + Math.abs(c - col);
                if (distance <= 2) {
                    // 距离越近，连接价值越高
                    connectivity += (3 - distance) * 20;

                    // 如果是同色棋子，额外加分
                    if (board[r][c] === 'white') {
                        connectivity += 30;
                    }
                }
            }
        }
    }

    return connectivity;
}

// 分析方向性
function analyzeDirectionality(row, col) {
    let directionality = 0;
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        let directionScore = 0;

        // 检查正方向
        let consecutiveEmpty = 0;
        let consecutiveWhite = 0;
        for (let i = 1; i <= 4; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;

            if (!isValidPosition(newRow, newCol)) break;

            if (board[newRow][newCol] === null) {
                consecutiveEmpty++;
            } else if (board[newRow][newCol] === 'white') {
                consecutiveWhite++;
                break;
            } else {
                break; // 遇到黑棋
            }
        }

        // 检查反方向
        let reverseEmpty = 0;
        let reverseWhite = 0;
        for (let i = 1; i <= 4; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;

            if (!isValidPosition(newRow, newCol)) break;

            if (board[newRow][newCol] === null) {
                reverseEmpty++;
            } else if (board[newRow][newCol] === 'white') {
                reverseWhite++;
                break;
            } else {
                break; // 遇到黑棋
            }
        }

        // 计算方向得分
        const totalEmpty = consecutiveEmpty + reverseEmpty;
        const totalWhite = consecutiveWhite + reverseWhite;

        if (totalEmpty >= 4) {
            directionScore += 100; // 有足够空间形成五连
        }

        if (totalWhite > 0) {
            directionScore += totalWhite * 50; // 有同色棋子支持
        }

        directionality += directionScore;
    }

    return directionality;
}

// 辅助函数：检查位置是否有效
function isValidPosition(row, col) {
    return row >= 0 && row < boardSize && col >= 0 && col < boardSize;
}

// 计算连续棋子数量
function countConsecutive(row, col, dx, dy, player) {
    let count = 1;

    // 向前计算
    for (let i = 1; i < 5; i++) {
        const newRow = row + i * dx;
        const newCol = col + i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else {
            break;
        }
    }

    // 向后计算
    for (let i = 1; i < 5; i++) {
        const newRow = row - i * dx;
        const newCol = col - i * dy;
        if (isValidPosition(newRow, newCol) && board[newRow][newCol] === player) {
            count++;
        } else {
            break;
        }
    }

    return count;
}

// 辅助函数：找到玩家第一步落子位置
function findPlayerFirstMove() {
    for (let r = 0; r < boardSize; r++) {
        for (let c = 0; c < boardSize; c++) {
            if (board[r][c] === 'black') {
                return { row: r, col: c };
            }
        }
    }
    return null;
}

// 带置换表的minimax算法
function minimaxWithTT(depth, isMaximizing, alpha, beta, transpositionTable) {
    // 生成当前棋盘状态的哈希键
    const boardHash = getBoardHash();

    // 查找置换表
    if (transpositionTable.has(boardHash)) {
        const entry = transpositionTable.get(boardHash);
        if (entry.depth >= depth) {
            if (entry.type === 'exact') {
                return entry.value;
            } else if (entry.type === 'lowerbound' && entry.value >= beta) {
                return entry.value;
            } else if (entry.type === 'upperbound' && entry.value <= alpha) {
                return entry.value;
            }
        }
    }

    let score = evaluateBoard();

    // 终止条件：达到最大深度或游戏结束
    if (score >= 100000 || score <= -100000 || depth === 0) {
        // 存储到置换表
        transpositionTable.set(boardHash, {
            value: score,
            depth: depth,
            type: 'exact'
        });
        return score;
    }

    // 获取当前层的候选位置
    const candidateMoves = getMiniMaxCandidates();
    const originalAlpha = alpha;
    let bestValue;

    if (isMaximizing) {
        bestValue = -Infinity;

        // 按启发式价值排序候选位置
        candidateMoves.sort((a, b) => {
            board[a.row][a.col] = 'white';
            const scoreA = evaluateBoard();
            board[a.row][a.col] = null;

            board[b.row][b.col] = 'white';
            const scoreB = evaluateBoard();
            board[b.row][b.col] = null;

            return scoreB - scoreA;
        });

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'white';
            const value = minimaxWithTT(depth - 1, false, alpha, beta, transpositionTable);
            board[row][col] = null;

            bestValue = Math.max(bestValue, value);
            alpha = Math.max(alpha, bestValue);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
    } else {
        bestValue = Infinity;

        // 按启发式价值排序候选位置
        candidateMoves.sort((a, b) => {
            board[a.row][a.col] = 'black';
            const scoreA = evaluateBoard();
            board[a.row][a.col] = null;

            board[b.row][b.col] = 'black';
            const scoreB = evaluateBoard();
            board[b.row][b.col] = null;

            return scoreA - scoreB;
        });

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'black';
            const value = minimaxWithTT(depth - 1, true, alpha, beta, transpositionTable);
            board[row][col] = null;

            bestValue = Math.min(bestValue, value);
            beta = Math.min(beta, bestValue);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
    }

    // 存储到置换表
    let entryType;
    if (bestValue <= originalAlpha) {
        entryType = 'upperbound';
    } else if (bestValue >= beta) {
        entryType = 'lowerbound';
    } else {
        entryType = 'exact';
    }

    transpositionTable.set(boardHash, {
        value: bestValue,
        depth: depth,
        type: entryType
    });

    return bestValue;
}

// 生成棋盘状态的哈希值
function getBoardHash() {
    let hash = '';
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === 'white') {
                hash += '1';
            } else if (board[row][col] === 'black') {
                hash += '2';
            } else {
                hash += '0';
            }
        }
    }
    return hash;
}

// 保留原有的minimax函数作为备用
function minimax(depth, isMaximizing, alpha, beta) {
    let score = evaluateBoard();

    // 终止条件：达到最大深度或游戏结束
    if (score >= 100000 || score <= -100000 || depth === 0) {
        return score;
    }

    // 获取当前层的候选位置（只搜索有意义的位置）
    const candidateMoves = getMiniMaxCandidates();

    if (isMaximizing) {
        let best = -Infinity;

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'white';
            const value = minimax(depth - 1, false, alpha, beta);
            board[row][col] = null;

            best = Math.max(best, value);
            alpha = Math.max(alpha, best);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
        return best;
    } else {
        let best = Infinity;

        for (const {row, col} of candidateMoves) {
            board[row][col] = 'black';
            const value = minimax(depth - 1, true, alpha, beta);
            board[row][col] = null;

            best = Math.min(best, value);
            beta = Math.min(beta, best);

            if (beta <= alpha) {
                break; // Alpha-beta剪枝
            }
        }
        return best;
    }
}

// 获取minimax搜索的候选位置
function getMiniMaxCandidates() {
    const candidates = [];
    const searchRange = 2;

    // 收集所有已有棋子周围的空位
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                for (let r = Math.max(0, row - searchRange); r <= Math.min(boardSize - 1, row + searchRange); r++) {
                    for (let c = Math.max(0, col - searchRange); c <= Math.min(boardSize - 1, col + searchRange); c++) {
                        if (!board[r][c] && !candidates.some(m => m.row === r && m.col === c)) {
                            candidates.push({ row: r, col: c });
                        }
                    }
                }
            }
        }
    }

    // 限制候选位置数量以提高搜索效率
    return candidates.slice(0, 12);
}

// 改进的缓存系统
let evaluationCache = new Map();
let cacheHits = 0;
let cacheMisses = 0;
const MAX_CACHE_SIZE = 10000;

function evaluateBoard() {
    const boardHash = getBoardHash();

    // 检查缓存
    if (evaluationCache.has(boardHash)) {
        cacheHits++;
        return evaluationCache.get(boardHash);
    }

    cacheMisses++;

    let score = 0;
    score += evaluateLines(board); // Rows
    let transposedBoard = transpose(board);
    score += evaluateLines(transposedBoard); // Columns
    let diagonals = getDiagonals(board);
    score += evaluateLines(diagonals); // Diagonals

    // 缓存管理：如果缓存过大，清理一半
    if (evaluationCache.size >= MAX_CACHE_SIZE) {
        const keysToDelete = Array.from(evaluationCache.keys()).slice(0, MAX_CACHE_SIZE / 2);
        keysToDelete.forEach(key => evaluationCache.delete(key));
    }

    evaluationCache.set(boardHash, score);
    return score;
}

// 清理缓存的函数
function clearCaches() {
    evaluationCache.clear();
    cacheHits = 0;
    cacheMisses = 0;
}

// 获取缓存统计信息
function getCacheStats() {
    const total = cacheHits + cacheMisses;
    const hitRate = total > 0 ? (cacheHits / total * 100).toFixed(2) : 0;
    return {
        hits: cacheHits,
        misses: cacheMisses,
        hitRate: hitRate + '%',
        size: evaluationCache.size
    };
}

function evaluateLines(lines) {
    let score = 0;
    for (const line of lines) {
        score += evaluateLine(line);
    }
    return score;
}

function evaluateLine(line) {
    let score = 0;
    // 使用更精确的棋型识别（5个连续位置）
    for (let i = 0; i <= line.length - 5; i++) {
        let window = line.slice(i, i + 5);
        score += scoreWindow(window, 'white') - scoreWindow(window, 'black') * 1.1;
    }

    // 额外检查更长的窗口以识别复杂棋型
    for (let i = 0; i <= line.length - 6; i++) {
        let window = line.slice(i, i + 6);
        score += scoreExtendedWindow(window, 'white') - scoreExtendedWindow(window, 'black') * 1.1;
    }

    return score;
}

function scoreWindow(window, player) {
    let score = 0;
    let playerCount = window.filter(cell => cell === player).length;
    let opponent = player === 'white' ? 'black' : 'white';
    let opponentCount = window.filter(cell => cell === opponent).length;
    let emptyCount = window.filter(cell => cell === null).length;

    // 如果窗口被对手棋子阻挡，则无价值
    if (opponentCount > 0) {
        return 0;
    }

    // 精确的棋型评估
    if (playerCount === 5) {
        score += 100000; // 五连
    } else if (playerCount === 4 && emptyCount === 1) {
        // 检查是否为真正的活四
        if (isLiveFour(window, player)) {
            score += 50000; // 活四
        } else {
            score += 15000; // 冲四
        }
    } else if (playerCount === 3 && emptyCount === 2) {
        // 检查具体的三子棋型
        const patternScore = analyzeThreePattern(window, player);
        score += patternScore;
    } else if (playerCount === 2 && emptyCount === 3) {
        // 检查具体的二子棋型
        const patternScore = analyzeTwoPattern(window, player);
        score += patternScore;
    } else if (playerCount === 1 && emptyCount === 4) {
        score += 10; // 单子
    }

    return score;
}

// 分析三子棋型
function analyzeThreePattern(window, player) {
    const pattern = window.map(cell => cell === player ? 'X' : (cell === null ? '_' : 'O')).join('');

    // 活三模式
    if (pattern === '_XXX_') return 8000; // 标准活三
    if (pattern === 'X_XX_' || pattern === '_XX_X') return 6000; // 跳活三
    if (pattern === 'XX__X' || pattern === 'X__XX') return 4000; // 大跳活三

    // 眠三模式
    if (pattern === 'XXX__' || pattern === '__XXX') return 1500; // 连三
    if (pattern === 'XX_X_' || pattern === '_X_XX') return 1200; // 跳三
    if (pattern === 'X_X_X') return 1000; // 分散三

    return 800; // 其他三子组合
}

// 分析二子棋型
function analyzeTwoPattern(window, player) {
    const pattern = window.map(cell => cell === player ? 'X' : (cell === null ? '_' : 'O')).join('');

    // 活二模式
    if (pattern === '_XX__' || pattern === '__XX_') return 600; // 连二
    if (pattern === '_X_X_') return 500; // 跳二
    if (pattern === 'X___X') return 400; // 大跳二

    // 眠二模式
    if (pattern === 'XX___' || pattern === '___XX') return 200; // 边二
    if (pattern === 'X_X__' || pattern === '__X_X') return 150; // 跳边二

    return 100; // 其他二子组合
}

// 检查是否为特殊棋型组合
function checkSpecialPatterns(row, col, player) {
    let score = 0;

    // 检查双三
    const threeCount = countPatternType(row, col, player, 'three');
    if (threeCount >= 2) {
        score += 20000; // 双三
    }

    // 检查三四组合
    const fourCount = countPatternType(row, col, player, 'four');
    if (fourCount >= 1 && threeCount >= 1) {
        score += 30000; // 三四组合
    }

    // 检查双四
    if (fourCount >= 2) {
        score += 40000; // 双四
    }

    return score;
}

// 计算特定棋型的数量
function countPatternType(row, col, player, patternType) {
    let count = 0;
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        const line = getLineFromPosition(row, col, dx, dy, 9);
        const centerIndex = Math.floor(line.length / 2);

        // 检查5格窗口
        for (let i = Math.max(0, centerIndex - 4); i <= Math.min(line.length - 5, centerIndex); i++) {
            const window = line.slice(i, i + 5);

            if (patternType === 'three') {
                const playerCount = window.filter(cell => cell === player).length;
                const emptyCount = window.filter(cell => cell === null).length;
                const opponent = player === 'white' ? 'black' : 'white';
                const opponentCount = window.filter(cell => cell === opponent).length;

                if (playerCount === 3 && emptyCount === 2 && opponentCount === 0) {
                    if (isLiveThree(window, player)) {
                        count++;
                    }
                }
            } else if (patternType === 'four') {
                const playerCount = window.filter(cell => cell === player).length;
                const emptyCount = window.filter(cell => cell === null).length;
                const opponent = player === 'white' ? 'black' : 'white';
                const opponentCount = window.filter(cell => cell === opponent).length;

                if (playerCount === 4 && emptyCount === 1 && opponentCount === 0) {
                    count++;
                }
            }
        }
    }

    return count;
}

function scoreExtendedWindow(window, player) {
    let score = 0;
    let playerCount = window.filter(cell => cell === player).length;
    let opponent = player === 'white' ? 'black' : 'white';
    let opponentCount = window.filter(cell => cell === opponent).length;

    // 如果窗口被对手棋子阻挡，则无价值
    if (opponentCount > 0) {
        return 0;
    }

    // 检查特殊棋型：跳三、跳四等
    if (playerCount === 4) {
        score += 8000; // 在6格窗口中的四子
    } else if (playerCount === 3) {
        score += 800; // 在6格窗口中的三子
    }

    return score;
}

// 判断是否为活四
function isLiveFour(window, player) {
    const playerCount = window.filter(cell => cell === player).length;
    const emptyCount = window.filter(cell => cell === null).length;

    if (playerCount !== 4 || emptyCount !== 1) return false;

    // 检查四子的连续性和空位位置
    const emptyIndex = window.indexOf(null);

    // 活四：四子连续且空位在两端或中间合适位置
    if (emptyIndex === 0 || emptyIndex === 4) {
        // 空位在两端，检查四子是否连续
        const playerIndices = [];
        for (let i = 0; i < window.length; i++) {
            if (window[i] === player) {
                playerIndices.push(i);
            }
        }
        return playerIndices.length === 4 &&
               playerIndices[3] - playerIndices[0] === 3;
    } else {
        // 空位在中间，检查是否形成活四
        return true;
    }
}

// 判断是否为活三
function isLiveThree(window, player) {
    const playerCount = window.filter(cell => cell === player).length;
    const emptyCount = window.filter(cell => cell === null).length;

    if (playerCount !== 3 || emptyCount !== 2) return false;

    // 检查三子是否有足够的延伸空间
    const firstEmpty = window.indexOf(null);
    const lastEmpty = window.lastIndexOf(null);

    // 活三需要两端都有空位或者中间有空位
    return (firstEmpty === 0 && lastEmpty === 4) ||
           (firstEmpty > 0 && firstEmpty < 4 && lastEmpty === 4) ||
           (firstEmpty === 0 && lastEmpty > 0 && lastEmpty < 4);
}

// 判断是否为活二
function isLiveTwo(window, player) {
    const playerCount = window.filter(cell => cell === player).length;
    const emptyCount = window.filter(cell => cell === null).length;

    if (playerCount !== 2 || emptyCount !== 3) return false;

    // 活二需要有延伸的可能性
    const firstPlayer = window.indexOf(player);
    const lastPlayer = window.lastIndexOf(player);

    return firstPlayer > 0 && lastPlayer < 4;
}

function transpose(matrix) {
    return matrix[0].map((_, i) => matrix.map(row => row[i]));
}

function getDiagonals(matrix) {
    const diagonals = [];
    const n = matrix.length;
    const m = matrix[0].length;

    for (let k = 0; k < n + m - 1; k++) {
        const diag = [];
        for (let j = 0; j <= k; j++) {
            const i = k - j;
            if (i < n && j < m) {
                diag.push(matrix[i][j]);
            }
        }
        if (diag.length >= 5) diagonals.push(diag);
    }

    for (let k = 0; k < n + m - 1; k++) {
        const diag = [];
        for (let j = 0; j <= k; j++) {
            const i = k - j;
            if (i < n && j < m) {
                diag.push(matrix[i][m - 1 - j]);
            }
        }
        if (diag.length >= 5) diagonals.push(diag);
    }
    return diagonals;
}

function checkWin(row, col, player) {
    const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
    ];

    for (const [dx, dy] of directions) {
        let count = 1;
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;
            if (newRow >= 0 && newRow < boardSize && newCol >= 0 && newCol < boardSize && board[newRow][newCol] === player) {
                count++;
            } else {
                break;
            }
        }
        if (count >= 5) {
            return true;
        }
    }
    return false;
}

resetButton.addEventListener('click', initBoard);

    // 快速威胁检测系统 - 优化响应速度
    function findCriticalDefense() {
        console.log('=== 快速威胁检测 ===');

        // 只检查最关键的威胁：获胜
        const immediateWin = findWinningMove('black');
        if (immediateWin) {
            console.log('🚨 发现获胜威胁:', immediateWin);
            return immediateWin;
        }

        // 快速检查活四（简化版）
        for (let row = 0; row < boardSize; row++) {
            for (let col = 0; col < boardSize; col++) {
                if (board[row][col] === null) {
                    board[row][col] = 'black';

                    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];
                    for (const [dx, dy] of directions) {
                        const count = countConsecutive(row, col, dx, dy, 'black');
                        if (count >= 4) {
                            board[row][col] = null;
                            console.log('⚠️ 发现活四威胁:', { row, col });
                            return { row, col };
                        }
                    }

                    board[row][col] = null;
                }
            }
        }

        console.log('✅ 未发现关键威胁');
        return null;
    }

// 新增：专门检测玩家的活三威胁
function findPlayerActiveThree() {
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === null) {
                // 模拟玩家在此位置形成活三
                board[row][col] = 'black';
                
                // 检查是否形成活三
                if (checkActiveThree(row, col, 'black')) {
                    board[row][col] = null;
                    return { row, col };
                }
                
                board[row][col] = null;
            }
        }
    }
    return null;
}

// 评估威胁等级
function evaluateThreatLevel(row, col, player) {
    let maxThreat = 0;
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        const threat = analyzeThreatInDirection(row, col, dx, dy, player);
        maxThreat = Math.max(maxThreat, threat);
    }

    return maxThreat;
}

// 分析特定方向的威胁
function analyzeThreatInDirection(row, col, dx, dy, player) {
    // 获取这个方向上的连续棋子信息
    const lineInfo = getLineInfo(row, col, dx, dy, player);

    // 根据连续棋子数量和空位情况评估威胁
    if (lineInfo.consecutive >= 5) {
        return 10000; // 立即获胜
    }

    if (lineInfo.consecutive === 4) {
        if (lineInfo.openEnds >= 1) {
            return 9000; // 活四
        } else {
            return 5000; // 冲四
        }
    }

    if (lineInfo.consecutive === 3) {
        if (lineInfo.openEnds >= 2) {
            return 3000; // 活三
        } else if (lineInfo.openEnds === 1) {
            return 1000; // 眠三
        }
    }

    if (lineInfo.consecutive === 2) {
        if (lineInfo.openEnds >= 2) {
            return 300; // 活二
        } else if (lineInfo.openEnds === 1) {
            return 100; // 眠二
        }
    }

    // 检查跳跃式威胁
    const jumpThreat = analyzeJumpThreat(row, col, dx, dy, player);
    return Math.max(0, jumpThreat);
}

// 获取直线信息
function getLineInfo(row, col, dx, dy, player) {
    let consecutive = 1; // 包含当前位置
    let openEnds = 0;

    // 向前检查
    let frontCount = 0;
    let frontBlocked = false;
    for (let i = 1; i <= 4; i++) {
        const newRow = row + i * dx;
        const newCol = col + i * dy;

        if (!isValidPosition(newRow, newCol)) {
            frontBlocked = true;
            break;
        }

        if (board[newRow][newCol] === player) {
            frontCount++;
        } else if (board[newRow][newCol] === null) {
            if (frontCount > 0) break; // 遇到空位，停止计数
            break;
        } else {
            frontBlocked = true;
            break;
        }
    }

    // 向后检查
    let backCount = 0;
    let backBlocked = false;
    for (let i = 1; i <= 4; i++) {
        const newRow = row - i * dx;
        const newCol = col - i * dy;

        if (!isValidPosition(newRow, newCol)) {
            backBlocked = true;
            break;
        }

        if (board[newRow][newCol] === player) {
            backCount++;
        } else if (board[newRow][newCol] === null) {
            if (backCount > 0) break; // 遇到空位，停止计数
            break;
        } else {
            backBlocked = true;
            break;
        }
    }

    consecutive += frontCount + backCount;

    // 计算开放端数量
    if (!frontBlocked) openEnds++;
    if (!backBlocked) openEnds++;

    return { consecutive, openEnds, frontCount, backCount };
}

// 分析跳跃式威胁（如 X_XX, XX_X 等）
function analyzeJumpThreat(row, col, dx, dy, player) {
    let maxJumpThreat = 0;

    // 检查各种跳跃模式
    const patterns = [
        // 跳活三模式: X_XX, XX_X
        { pattern: [player, null, player, player], threat: 2000 },
        { pattern: [player, player, null, player], threat: 2000 },
        // 跳活二模式: X_X_, _X_X
        { pattern: [player, null, player, null], threat: 200 },
        { pattern: [null, player, null, player], threat: 200 },
    ];

    // 检查正向和反向的模式
    for (let direction = -1; direction <= 1; direction += 2) {
        for (const { pattern, threat } of patterns) {
            if (matchesPattern(row, col, dx * direction, dy * direction, pattern)) {
                maxJumpThreat = Math.max(maxJumpThreat, threat);
            }
        }
    }

    return maxJumpThreat;
}

// 检查是否匹配特定模式
function matchesPattern(startRow, startCol, dx, dy, pattern) {
    for (let i = 0; i < pattern.length; i++) {
        const checkRow = startRow + i * dx;
        const checkCol = startCol + i * dy;

        if (!isValidPosition(checkRow, checkCol)) {
            return false;
        }

        const expected = pattern[i];
        const actual = board[checkRow][checkCol];

        if (expected !== actual) {
            return false;
        }
    }
    return true;
}

// 获取威胁描述
function getThreatDescription(level) {
    if (level >= 10000) return '立即获胜';
    if (level >= 9000) return '活四';
    if (level >= 5000) return '冲四';
    if (level >= 3000) return '活三';
    if (level >= 2000) return '跳活三';
    if (level >= 1000) return '眠三';
    if (level >= 300) return '活二';
    if (level >= 200) return '跳活二';
    if (level >= 100) return '眠二';
    return '轻微威胁';
}

// 检查活四
function checkActiveFour(row, col, player) {
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        let count = 1; // 包含当前位置
        let frontBlocked = false;
        let backBlocked = false;

        // 向前计数
        for (let i = 1; i < 5; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;

            if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== player) {
                if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== null) {
                    frontBlocked = true;
                }
                break;
            }
            count++;
        }

        // 向后计数
        for (let i = 1; i < 5; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;

            if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== player) {
                if (!isValidPosition(newRow, newCol) || board[newRow][newCol] !== null) {
                    backBlocked = true;
                }
                break;
            }
            count++;
        }

        // 活四：4个连子且至少一端不被阻挡
        if (count === 4 && (!frontBlocked || !backBlocked)) {
            return true;
        }
    }

    return false;
}

// 检查活三
function checkActiveThree(row, col, player) {
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        let count = 1; // 包含当前位置

        let frontEmpty = 0;
        let backEmpty = 0;

        // 向前检查
        for (let i = 1; i <= 4; i++) {
            const newRow = row + i * dx;
            const newCol = col + i * dy;

            if (!isValidPosition(newRow, newCol)) {
                break;
            }

            if (board[newRow][newCol] === player) {
                count++;
            } else if (board[newRow][newCol] === null) {
                frontEmpty++;
                break;
            } else {
                break;
            }
        }

        // 向后检查
        for (let i = 1; i <= 4; i++) {
            const newRow = row - i * dx;
            const newCol = col - i * dy;

            if (!isValidPosition(newRow, newCol)) {
                break;
            }

            if (board[newRow][newCol] === player) {
                count++;
            } else if (board[newRow][newCol] === null) {
                backEmpty++;
                break;
            } else {
                break;
            }
        }

        // 活三：3个连子且两端都有空位可以延伸
        if (count === 3 && frontEmpty > 0 && backEmpty > 0) {
            return true;
        }
    }

    return false;
}

// 测试威胁检测功能
function testThreatDetection() {
    console.log('=== 威胁检测测试 ===');

    // 清空棋盘
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));

    // 测试场景1：活三
    const center = Math.floor(boardSize / 2);
    board[center][center] = 'black';
    board[center][center + 1] = 'black';
    board[center][center + 2] = 'black';

    console.log('测试场景1：黑棋在中心形成活三');
    console.log('黑棋位置：', `(${center},${center})`, `(${center},${center + 1})`, `(${center},${center + 2})`);

    let criticalDefense = findCriticalDefense();
    console.log('关键防守位置：', criticalDefense);

    // 清空棋盘，测试场景2：跳活三
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    board[center][center] = 'black';
    board[center][center + 2] = 'black';
    board[center][center + 3] = 'black';

    console.log('\n测试场景2：黑棋形成跳活三 X_XX');
    criticalDefense = findCriticalDefense();
    console.log('关键防守位置：', criticalDefense);

    // 清空棋盘，测试场景3：活四
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    board[center][center] = 'black';
    board[center][center + 1] = 'black';
    board[center][center + 2] = 'black';
    board[center][center + 3] = 'black';

    console.log('\n测试场景3：黑棋形成活四');
    criticalDefense = findCriticalDefense();
    console.log('关键防守位置：', criticalDefense);

    // 恢复棋盘
    board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(null));
    console.log('\n=== 测试完成 ===');
}

// 增强的威胁检测 - 添加更多威胁模式检测
function findAdvancedThreats() {
    console.log('=== 高级威胁检测 ===');

    const allThreats = [];

    // 检查现有棋盘上的威胁模式
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === 'black') {
                // 从每个黑棋位置检查威胁
                const threats = checkThreatsFromPosition(row, col, 'black');
                allThreats.push(...threats);
            }
        }
    }

    // 去重并排序
    const uniqueThreats = removeDuplicateThreats(allThreats);
    uniqueThreats.sort((a, b) => b.level - a.level);

    if (uniqueThreats.length > 0) {
        console.log('发现的威胁:', uniqueThreats);
        return uniqueThreats[0];
    }

    return null;
}

// 从特定位置检查威胁
function checkThreatsFromPosition(row, col, player) {
    const threats = [];
    const directions = [[1, 0], [0, 1], [1, 1], [1, -1]];

    for (const [dx, dy] of directions) {
        // 检查这个方向上需要防守的位置
        const defensePositions = findDefensePositionsInDirection(row, col, dx, dy, player);
        threats.push(...defensePositions);
    }

    return threats;
}

// 在特定方向寻找防守位置
function findDefensePositionsInDirection(row, col, dx, dy, player) {
    const threats = [];

    // 检查从当前位置延伸的各种威胁模式
    for (let range = 1; range <= 5; range++) {
        for (let offset = -range; offset <= range; offset++) {
            const checkRow = row + offset * dx;
            const checkCol = col + offset * dy;

            if (isValidPosition(checkRow, checkCol) && board[checkRow][checkCol] === null) {
                // 模拟在此位置放置棋子，检查威胁等级
                board[checkRow][checkCol] = player;
                const threatLevel = evaluateThreatLevel(checkRow, checkCol, player);
                board[checkRow][checkCol] = null;

                if (threatLevel >= 1000) { // 只关注重要威胁
                    threats.push({
                        row: checkRow,
                        col: checkCol,
                        level: threatLevel,
                        description: getThreatDescription(threatLevel)
                    });
                }
            }
        }
    }

    return threats;
}

// 去除重复威胁
function removeDuplicateThreats(threats) {
    const seen = new Set();
    const unique = [];

    for (const threat of threats) {
        const key = `${threat.row},${threat.col}`;
        if (!seen.has(key)) {
            seen.add(key);
            unique.push(threat);
        }
    }

    return unique;
}

// 在控制台中可以调用 testThreatDetection() 来测试
window.testThreatDetection = testThreatDetection;

resetButton.addEventListener('click', initBoard);

initBoard();
