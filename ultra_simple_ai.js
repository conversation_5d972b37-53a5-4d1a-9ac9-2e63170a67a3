// 超极简AI - 如果还有问题可以用这个替换
async function findBestMoveAsync() {
    console.log('🤖 超极简AI开始');
    
    // 1. 检查AI是否能获胜
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === null) {
                board[row][col] = 'white';
                if (checkWin(row, col, 'white')) {
                    board[row][col] = null;
                    console.log(`🎯 AI获胜: (${row}, ${col})`);
                    return { row, col };
                }
                board[row][col] = null;
            }
        }
    }
    
    // 2. 检查是否需要防守
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === null) {
                board[row][col] = 'black';
                if (checkWin(row, col, 'black')) {
                    board[row][col] = null;
                    console.log(`🛡️ AI防守: (${row}, ${col})`);
                    return { row, col };
                }
                board[row][col] = null;
            }
        }
    }
    
    // 3. 找第一个邻近位置
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col]) {
                // 检查周围8个位置
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue;
                        const newRow = row + dr;
                        const newCol = col + dc;
                        
                        if (newRow >= 0 && newRow < boardSize && 
                            newCol >= 0 && newCol < boardSize && 
                            board[newRow][newCol] === null) {
                            console.log(`⚡ AI选择: (${newRow}, ${newCol})`);
                            return { row: newRow, col: newCol };
                        }
                    }
                }
            }
        }
    }
    
    // 4. 选择中心位置
    const center = Math.floor(boardSize / 2);
    if (board[center][center] === null) {
        console.log(`⚡ AI选择中心: (${center}, ${center})`);
        return { row: center, col: center };
    }
    
    // 5. 找任意空位
    for (let row = 0; row < boardSize; row++) {
        for (let col = 0; col < boardSize; col++) {
            if (board[row][col] === null) {
                console.log(`⚡ AI选择: (${row}, ${col})`);
                return { row, col };
            }
        }
    }
    
    return null;
}
